#!/usr/bin/env tsx

/**
 * Generate the correct bcrypt hash for admin123 password
 */

import bcrypt from 'bcryptjs';

async function generateAdminHash() {
  console.log('🔐 Generating bcrypt hash for "admin123" with 10 salt rounds...\n');
  
  // Generate hash with exactly 10 salt rounds
  const hash = await bcrypt.hash('admin123', 10);
  console.log('Generated hash:');
  console.log(hash);
  
  // Verify the hash works
  const isValid = await bcrypt.compare('admin123', hash);
  console.log(`\nVerification: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
  
  console.log('\n📋 Use this hash in your migration file:');
  console.log(`'${hash}'`);
}

generateAdminHash();
