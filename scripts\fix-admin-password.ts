#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to fix the admin password issue
 * Sets the admin password to "admin123" with proper bcrypt hashing
 */

import bcrypt from 'bcryptjs';
import { db } from '../src/database/connection';

async function fixAdminPassword() {
  console.log('🔧 Fixing admin password...\n');

  try {
    // Check database connection
    const isHealthy = await db.healthCheck();
    if (!isHealthy) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful\n');

    // Generate correct hash for admin123 with exactly 10 salt rounds
    console.log('🔐 Generating password hash for "admin123"...');
    const correctHash = await bcrypt.hash('admin123', 10);
    console.log('✅ Password hash generated\n');

    // Update or create admin user
    console.log('👤 Updating admin user...');
    
    // First, try to update existing admin user
    const updateResult = await db.query(`
      UPDATE users 
      SET name = 'admin',
          email = '<EMAIL>',
          password_hash = $1,
          updated_at = CURRENT_TIMESTAMP
      WHERE role = 'admin'
      RETURNING id, name, email, role
    `, [correctHash]);

    if (updateResult.rows.length > 0) {
      console.log('✅ Existing admin user updated:');
      const admin = updateResult.rows[0];
      console.log(`   ID: ${admin.id}`);
      console.log(`   Username: ${admin.name}`);
      console.log(`   Email: ${admin.email}`);
      console.log(`   Role: ${admin.role}\n`);
    } else {
      // No admin user exists, create one
      console.log('ℹ️  No admin user found, creating new one...');
      
      const insertResult = await db.query(`
        INSERT INTO users (email, name, password_hash, role)
        VALUES ('<EMAIL>', 'admin', $1, 'admin')
        RETURNING id, name, email, role
      `, [correctHash]);

      if (insertResult.rows.length > 0) {
        console.log('✅ New admin user created:');
        const admin = insertResult.rows[0];
        console.log(`   ID: ${admin.id}`);
        console.log(`   Username: ${admin.name}`);
        console.log(`   Email: ${admin.email}`);
        console.log(`   Role: ${admin.role}\n`);
      } else {
        throw new Error('Failed to create admin user');
      }
    }

    // Verify the password works
    console.log('🧪 Testing login credentials...');
    const testResult = await db.query(`
      SELECT id, name, password_hash 
      FROM users 
      WHERE name = 'admin' AND role = 'admin'
    `);

    if (testResult.rows.length > 0) {
      const admin = testResult.rows[0];
      const passwordMatch = await bcrypt.compare('admin123', admin.password_hash);
      
      if (passwordMatch) {
        console.log('✅ Password verification successful!');
        console.log('\n🎉 Admin credentials are now working:');
        console.log('   Username: admin');
        console.log('   Password: admin123');
        console.log('\n📝 You can now login with these credentials.');
      } else {
        console.log('❌ Password verification failed');
      }
    } else {
      console.log('❌ Admin user not found after update');
    }

    // Show all admin users for verification
    console.log('\n📊 All admin users in database:');
    const allAdmins = await db.query(`
      SELECT id, name, email, role, created_at 
      FROM users 
      WHERE role = 'admin'
      ORDER BY id
    `);

    if (allAdmins.rows.length > 0) {
      allAdmins.rows.forEach((admin, index) => {
        console.log(`   ${index + 1}. ${admin.name} (${admin.email}) - ID: ${admin.id}`);
      });
    } else {
      console.log('   No admin users found');
    }

  } catch (error) {
    console.error('\n❌ Error fixing admin password:', error);
    process.exit(1);
  } finally {
    await db.disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the fix
if (require.main === module) {
  fixAdminPassword();
}

export { fixAdminPassword };
