import { db } from '../connection';
import { User, CreateUserRequest, UpdateUserRequest, CreateUserByUsernameRequest } from '../../types';
import { NotFoundError, ConflictError } from '../../utils/errors';
import bcrypt from 'bcryptjs';

export class UserModel {
  /**
   * Helper method to map database row to User object
   */
  private static mapRowToUser(row: any): User {
    return {
      id: row.id,
      email: row.email,
      name: row.name,
      role: row.role,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  /**
   * Helper method to map database row to User object with password
   */
  private static mapRowToUserWithPassword(row: any): User & { password_hash: string } {
    return {
      id: row.id,
      email: row.email,
      name: row.name,
      role: row.role,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      password_hash: row.password_hash
    };
  }

  static async findAll(): Promise<User[]> {
    const result = await db.query(`
      SELECT id, email, name, role, created_at, updated_at
      FROM users
      ORDER BY created_at DESC
    `);

    // Map database column names to TypeScript interface
    return result.rows.map((row: any) => this.mapRowToUser(row));
  }

  static async findById(id: number): Promise<User | null> {
    const result = await db.query(`
      SELECT id, email, name, role, created_at, updated_at
      FROM users
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) return null;

    return this.mapRowToUser(result.rows[0]);
  }

  static async findByEmail(email: string): Promise<User | null> {
    const result = await db.query(`
      SELECT id, email, name, role, created_at, updated_at
      FROM users
      WHERE email = $1
    `, [email]);

    return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
  }

  static async findByUsername(username: string): Promise<User | null> {
    const result = await db.query(`
      SELECT id, email, name, role, created_at, updated_at
      FROM users
      WHERE name = $1
    `, [username]);

    return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
  }

  static async findByEmailWithPassword(email: string): Promise<(User & { password_hash: string }) | null> {
    const result = await db.query(`
      SELECT id, email, name, role, password_hash, created_at, updated_at
      FROM users
      WHERE email = $1
    `, [email]);

    return result.rows.length > 0 ? this.mapRowToUserWithPassword(result.rows[0]) : null;
  }

  static async findByUsernameWithPassword(username: string): Promise<(User & { password_hash: string }) | null> {
    const result = await db.query(`
      SELECT id, email, name, role, password_hash, created_at, updated_at
      FROM users
      WHERE name = $1
    `, [username]);

    return result.rows.length > 0 ? this.mapRowToUserWithPassword(result.rows[0]) : null;
  }

  static async create(userData: CreateUserRequest): Promise<User> {
    // Check if user already exists by email
    const existingUserByEmail = await this.findByEmail(userData.email);
    if (existingUserByEmail) {
      throw new ConflictError('User with this email already exists');
    }

    // Check if username already exists
    const existingUserByUsername = await this.findByUsername(userData.name);
    if (existingUserByUsername) {
      throw new ConflictError('User with this username already exists');
    }

    // Hash password with exactly 10 salt rounds as specified
    const passwordHash = await bcrypt.hash(userData.password, 10);

    const result = await db.query(`
      INSERT INTO users (email, name, password_hash, role)
      VALUES ($1, $2, $3, $4)
      RETURNING id, email, name, role, created_at, updated_at
    `, [userData.email, userData.name, passwordHash, userData.role || 'user']);

    return this.mapRowToUser(result.rows[0]);
  }

  static async createByUsername(userData: CreateUserByUsernameRequest): Promise<User> {
    // Check if username already exists
    const existingUser = await this.findByUsername(userData.username);
    if (existingUser) {
      throw new ConflictError('User with this username already exists');
    }

    // Hash password with exactly 10 salt rounds as specified
    const passwordHash = await bcrypt.hash(userData.password, 10);

    // Generate a placeholder email since the schema requires it
    const placeholderEmail = `${userData.username}@teleai.local`;

    const result = await db.query(`
      INSERT INTO users (email, name, password_hash, role)
      VALUES ($1, $2, $3, $4)
      RETURNING id, email, name, role, created_at, updated_at
    `, [placeholderEmail, userData.username, passwordHash, userData.role || 'user']);

    return result.rows[0];
  }

  static async update(id: number, userData: UpdateUserRequest): Promise<User> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Check email uniqueness if email is being updated
    if (userData.email && userData.email !== user.email) {
      const existingUser = await this.findByEmail(userData.email);
      if (existingUser) {
        throw new ConflictError('User with this email already exists');
      }
    }

    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramCount = 1;

    if (userData.name) {
      updateFields.push(`name = $${paramCount++}`);
      updateValues.push(userData.name);
    }

    if (userData.email) {
      updateFields.push(`email = $${paramCount++}`);
      updateValues.push(userData.email);
    }

    if (userData.role) {
      updateFields.push(`role = $${paramCount++}`);
      updateValues.push(userData.role);
    }

    if (userData.password) {
      const passwordHash = await bcrypt.hash(userData.password, 10);
      updateFields.push(`password_hash = $${paramCount++}`);
      updateValues.push(passwordHash);
    }

    if (updateFields.length === 0) {
      return user;
    }

    updateValues.push(id);

    const result = await db.query(`
      UPDATE users 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING id, email, name, role, created_at, updated_at
    `, updateValues);

    return result.rows[0];
  }

  static async delete(id: number): Promise<void> {
    const result = await db.query('DELETE FROM users WHERE id = $1', [id]);
    
    if (result.rowCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  static async verifyPassword(email: string, password: string): Promise<User | null> {
    const user = await this.findByEmailWithPassword(email);
    if (!user) {
      return null;
    }

    const isValid = await bcrypt.compare(password, user.password_hash);
    if (!isValid) {
      return null;
    }

    // Return user without password hash
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  static async verifyPasswordByUsername(username: string, password: string): Promise<User | null> {
    const user = await this.findByUsernameWithPassword(username);
    if (!user) {
      return null;
    }

    const isValid = await bcrypt.compare(password, user.password_hash);
    if (!isValid) {
      return null;
    }

    // Return user without password hash
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  /**
   * Update user's password with proper validation and hashing
   * @param userId - User ID
   * @param newPassword - New password (plain text)
   * @returns Updated user without password hash
   */
  static async updatePassword(userId: number, newPassword: string): Promise<User> {
    // Validate password requirements
    if (!newPassword || typeof newPassword !== 'string') {
      throw new Error('Password is required and must be a string');
    }

    if (newPassword.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    // Check if user exists
    const user = await this.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Hash the new password with exactly 10 salt rounds
    const passwordHash = await bcrypt.hash(newPassword, 10);

    // Update password in database
    const result = await db.query(`
      UPDATE users
      SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING id, email, name, role, created_at, updated_at
    `, [passwordHash, userId]);

    return result.rows[0];
  }

  /**
   * Update user's password by username (for admin operations)
   * @param username - Username
   * @param newPassword - New password (plain text)
   * @returns Updated user without password hash
   */
  static async updatePasswordByUsername(username: string, newPassword: string): Promise<User> {
    // Validate password requirements
    if (!newPassword || typeof newPassword !== 'string') {
      throw new Error('Password is required and must be a string');
    }

    if (newPassword.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    // Check if user exists
    const user = await this.findByUsername(username);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Hash the new password with exactly 10 salt rounds
    const passwordHash = await bcrypt.hash(newPassword, 10);

    // Update password in database
    const result = await db.query(`
      UPDATE users
      SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
      WHERE name = $2
      RETURNING id, email, name, role, created_at, updated_at
    `, [passwordHash, username]);

    return result.rows[0];
  }

  /**
   * Change user's password with current password verification
   * @param userId - User ID
   * @param currentPassword - Current password for verification
   * @param newPassword - New password (plain text)
   * @returns Updated user without password hash
   */
  static async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<User> {
    // Validate new password requirements
    if (!newPassword || typeof newPassword !== 'string') {
      throw new Error('New password is required and must be a string');
    }

    if (newPassword.length < 6) {
      throw new Error('New password must be at least 6 characters long');
    }

    if (!currentPassword || typeof currentPassword !== 'string') {
      throw new Error('Current password is required for verification');
    }

    // Get user with password hash for verification
    const userWithPassword = await db.query(`
      SELECT id, email, name, role, password_hash, created_at, updated_at
      FROM users
      WHERE id = $1
    `, [userId]);

    if (userWithPassword.rows.length === 0) {
      throw new NotFoundError('User not found');
    }

    const user = userWithPassword.rows[0];

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // Check if new password is different from current password
    const isSamePassword = await bcrypt.compare(newPassword, user.password_hash);
    if (isSamePassword) {
      throw new Error('New password must be different from current password');
    }

    // Hash the new password with exactly 10 salt rounds
    const passwordHash = await bcrypt.hash(newPassword, 10);

    // Update password in database
    const result = await db.query(`
      UPDATE users
      SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING id, email, name, role, created_at, updated_at
    `, [passwordHash, userId]);

    return result.rows[0];
  }

  static async count(): Promise<number> {
    const result = await db.query('SELECT COUNT(*) as count FROM users');
    return parseInt(result.rows[0].count);
  }
}

// Note: User request types are imported from ../../types

export { CreateUserRequest, UpdateUserRequest, CreateUserByUsernameRequest };
