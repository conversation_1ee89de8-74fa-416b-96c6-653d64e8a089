import { Response } from 'express';
import { UserModel } from '../database/models/User';
import { AuthenticatedRequest } from '../middleware/authMiddleware';
import { UpdatePasswordRequest, ChangePasswordRequest, AdminUpdatePasswordRequest } from '../types';
import logger from '../utils/logger';

/**
 * Password Management Controller
 * Handles password update and reset operations
 */
export class PasswordController {
  /**
   * Change user's own password - PUT /api/auth/change-password
   * Requires current password verification
   * 
   * @param req - Express request object with user data
   * @param res - Express response object
   */
  static async changePassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { currentPassword, newPassword } = req.body as ChangePasswordRequest;

      // Validate required fields
      if (!currentPassword || !newPassword) {
        logger.warn('Password change attempt with missing fields', {
          hasCurrentPassword: !!currentPassword,
          hasNewPassword: !!newPassword,
          userId: req.user?.id,
          ip: req.ip
        });

        res.status(400).json({
          error: {
            message: 'Current password and new password are required',
            code: 'MISSING_REQUIRED_FIELDS',
            statusCode: 400
          }
        });
        return;
      }

      // Validate new password strength
      if (typeof newPassword !== 'string' || newPassword.length < 6) {
        res.status(400).json({
          error: {
            message: 'New password must be at least 6 characters long',
            code: 'INVALID_PASSWORD',
            statusCode: 400
          }
        });
        return;
      }

      if (!req.user?.id) {
        res.status(401).json({
          error: {
            message: 'Authentication required',
            code: 'AUTHENTICATION_REQUIRED',
            statusCode: 401
          }
        });
        return;
      }

      // Change password with current password verification
      const updatedUser = await UserModel.changePassword(
        req.user.id,
        currentPassword,
        newPassword
      );

      logger.info('Password changed successfully', {
        userId: updatedUser.id,
        username: updatedUser.name,
        ip: req.ip
      });

      res.status(200).json({
        message: 'Password changed successfully',
        user: {
          id: updatedUser.id,
          username: updatedUser.name,
          role: updatedUser.role,
          email: updatedUser.email,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt
        }
      });
    } catch (error) {
      // Handle specific password change errors
      if (error instanceof Error) {
        if (error.message.includes('Current password is incorrect')) {
          logger.warn('Password change failed: Incorrect current password', {
            userId: req.user?.id,
            ip: req.ip
          });

          res.status(400).json({
            error: {
              message: 'Current password is incorrect',
              code: 'INVALID_CURRENT_PASSWORD',
              statusCode: 400
            }
          });
          return;
        }

        if (error.message.includes('New password must be different')) {
          res.status(400).json({
            error: {
              message: 'New password must be different from current password',
              code: 'SAME_PASSWORD',
              statusCode: 400
            }
          });
          return;
        }

        if (error.message.includes('User not found')) {
          res.status(404).json({
            error: {
              message: 'User not found',
              code: 'USER_NOT_FOUND',
              statusCode: 404
            }
          });
          return;
        }
      }

      logger.error('Password change error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.id,
        ip: req.ip
      });

      res.status(500).json({
        error: {
          message: 'Internal server error during password change',
          code: 'PASSWORD_CHANGE_ERROR',
          statusCode: 500
        }
      });
    }
  }

  /**
   * Admin reset user password - PUT /api/admin/reset-password
   * Admin-only endpoint to reset any user's password
   * 
   * @param req - Express request object with user data
   * @param res - Express response object
   */
  static async adminResetPassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { username, newPassword } = req.body as AdminUpdatePasswordRequest;

      // Validate required fields
      if (!username || !newPassword) {
        logger.warn('Admin password reset attempt with missing fields', {
          hasUsername: !!username,
          hasNewPassword: !!newPassword,
          adminId: req.user?.id,
          ip: req.ip
        });

        res.status(400).json({
          error: {
            message: 'Username and new password are required',
            code: 'MISSING_REQUIRED_FIELDS',
            statusCode: 400
          }
        });
        return;
      }

      // Validate new password strength
      if (typeof newPassword !== 'string' || newPassword.length < 6) {
        res.status(400).json({
          error: {
            message: 'New password must be at least 6 characters long',
            code: 'INVALID_PASSWORD',
            statusCode: 400
          }
        });
        return;
      }

      // Validate username format
      if (typeof username !== 'string' || username.trim().length < 3) {
        res.status(400).json({
          error: {
            message: 'Username must be at least 3 characters long',
            code: 'INVALID_USERNAME',
            statusCode: 400
          }
        });
        return;
      }

      // Reset password by username
      const updatedUser = await UserModel.updatePasswordByUsername(
        username.trim(),
        newPassword
      );

      logger.info('Admin password reset successful', {
        targetUserId: updatedUser.id,
        targetUsername: updatedUser.name,
        adminId: req.user?.id,
        adminUsername: req.user?.username,
        ip: req.ip
      });

      res.status(200).json({
        message: 'Password reset successfully',
        user: {
          id: updatedUser.id,
          username: updatedUser.name,
          role: updatedUser.role,
          email: updatedUser.email,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt
        }
      });
    } catch (error) {
      // Handle specific password reset errors
      if (error instanceof Error && error.message.includes('User not found')) {
        logger.warn('Admin password reset failed: User not found', {
          username: req.body.username,
          adminId: req.user?.id,
          ip: req.ip
        });

        res.status(404).json({
          error: {
            message: 'User not found',
            code: 'USER_NOT_FOUND',
            statusCode: 404
          }
        });
        return;
      }

      logger.error('Admin password reset error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        adminId: req.user?.id,
        targetUsername: req.body.username,
        ip: req.ip
      });

      res.status(500).json({
        error: {
          message: 'Internal server error during password reset',
          code: 'PASSWORD_RESET_ERROR',
          statusCode: 500
        }
      });
    }
  }
}
