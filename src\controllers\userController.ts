import { Response } from 'express';
import { UserModel } from '../database/models/User';
import { AuthenticatedRequest } from '../middleware/authMiddleware';
import { CreateUserByUsernameRequest } from '../types';
import logger from '../utils/logger';

/**
 * User Management Controller
 * Handles admin-only user creation and management endpoints
 */
export class UserController {
  /**
   * Create user endpoint - POST /api/users
   * Admin-only endpoint to create new users with {username, password, role}
   * 
   * @param req - Express request object with user data
   * @param res - Express response object
   */
  static async createUser(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { username, password, role } = req.body;

      // Validate required fields
      if (!username || !password) {
        logger.warn('User creation attempt with missing fields', {
          hasUsername: !!username,
          hasPassword: !!password,
          adminId: req.user?.id,
          ip: req.ip
        });

        res.status(400).json({
          error: {
            message: 'Username and password are required',
            code: 'MISSING_REQUIRED_FIELDS',
            statusCode: 400
          }
        });
        return;
      }

      // Validate username format (basic validation)
      if (typeof username !== 'string' || username.trim().length < 3) {
        res.status(400).json({
          error: {
            message: 'Username must be at least 3 characters long',
            code: 'INVALID_USERNAME',
            statusCode: 400
          }
        });
        return;
      }

      // Validate password strength (basic validation)
      if (typeof password !== 'string' || password.length < 6) {
        res.status(400).json({
          error: {
            message: 'Password must be at least 6 characters long',
            code: 'INVALID_PASSWORD',
            statusCode: 400
          }
        });
        return;
      }

      // Validate role if provided
      if (role && !['admin', 'user'].includes(role)) {
        res.status(400).json({
          error: {
            message: 'Role must be either "admin" or "user"',
            code: 'INVALID_ROLE',
            statusCode: 400
          }
        });
        return;
      }

      // Prepare user data
      const userData: CreateUserByUsernameRequest = {
        username: username.trim(),
        password,
        role: role || 'user'
      };

      // Create the user
      const newUser = await UserModel.createByUsername(userData);

      logger.info('User created successfully', {
        newUserId: newUser.id,
        newUsername: newUser.name,
        newUserRole: newUser.role,
        createdBy: req.user?.id,
        createdByUsername: req.user?.username
      });

      // Return created user data (without sensitive information)
      res.status(201).json({
        user: {
          id: newUser.id,
          username: newUser.name,
          role: newUser.role,
          email: newUser.email,
          createdAt: newUser.createdAt,
          updatedAt: newUser.updatedAt
        }
      });
    } catch (error) {
      // Handle specific database errors
      if (error instanceof Error && error.message.includes('already exists')) {
        logger.warn('User creation failed: Username already exists', {
          username: req.body.username,
          adminId: req.user?.id,
          ip: req.ip
        });

        res.status(409).json({
          error: {
            message: 'Username already exists',
            code: 'USERNAME_EXISTS',
            statusCode: 409
          }
        });
        return;
      }

      logger.error('User creation error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        adminId: req.user?.id,
        requestData: {
          username: req.body.username,
          role: req.body.role
        },
        ip: req.ip
      });

      res.status(500).json({
        error: {
          message: 'Internal server error during user creation',
          code: 'USER_CREATION_ERROR',
          statusCode: 500
        }
      });
    }
  }

  /**
   * Get all users endpoint - GET /api/users (optional for admin dashboard)
   * Admin-only endpoint to list all users
   * 
   * @param req - Express request object
   * @param res - Express response object
   */
  static async getAllUsers(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const users = await UserModel.findAll();

      logger.info('Users list retrieved', {
        count: users.length,
        adminId: req.user?.id
      });

      // Return users without sensitive information
      const sanitizedUsers = users.map(user => ({
        id: user.id,
        username: user.name,
        role: user.role,
        email: user.email,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }));

      res.status(200).json({
        users: sanitizedUsers,
        count: users.length
      });
    } catch (error) {
      logger.error('Get users error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        adminId: req.user?.id,
        ip: req.ip
      });

      res.status(500).json({
        error: {
          message: 'Internal server error during users retrieval',
          code: 'USERS_RETRIEVAL_ERROR',
          statusCode: 500
        }
      });
    }
  }
}
