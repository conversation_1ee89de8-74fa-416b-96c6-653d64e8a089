import request from 'supertest';
import express from 'express';
import { UserModel } from '../database/models/User';
import authRoutes from '../routes/authRoutes';
import { generateToken } from '../utils/tokenUtils';
import { User } from '../types';

// Create test app
const app = express();
app.use(express.json());
app.use('/api/auth', authRoutes);

// Mock the UserModel
jest.mock('../database/models/User');
const mockUserModel = UserModel as jest.Mocked<typeof UserModel>;

// Mock logger to avoid console output during tests
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('Authentication API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/login', () => {
    const validUser: User = {
      id: 1,
      name: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('should login successfully with valid credentials', async () => {
      mockUserModel.verifyPasswordByUsername.mockResolvedValue(validUser);

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toEqual({
        id: validUser.id,
        username: validUser.name,
        role: validUser.role,
        email: validUser.email,
        createdAt: validUser.createdAt.toISOString(),
        updatedAt: validUser.updatedAt.toISOString()
      });
      expect(mockUserModel.verifyPasswordByUsername).toHaveBeenCalledWith('testuser', 'password123');
    });

    it('should return 401 for invalid username', async () => {
      mockUserModel.verifyPasswordByUsername.mockResolvedValue(null);

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'invaliduser',
          password: 'password123'
        });

      expect(response.status).toBe(401);
      expect(response.body.error).toEqual({
        message: 'Invalid username or password',
        code: 'INVALID_CREDENTIALS',
        statusCode: 401
      });
    });

    it('should return 401 for invalid password', async () => {
      mockUserModel.verifyPasswordByUsername.mockResolvedValue(null);

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.error).toEqual({
        message: 'Invalid username or password',
        code: 'INVALID_CREDENTIALS',
        statusCode: 401
      });
    });

    it('should return 400 for missing username', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          password: 'password123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username and password are required',
        code: 'MISSING_CREDENTIALS',
        statusCode: 400
      });
    });

    it('should return 400 for missing password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username and password are required',
        code: 'MISSING_CREDENTIALS',
        statusCode: 400
      });
    });

    it('should return 400 for missing both fields', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username and password are required',
        code: 'MISSING_CREDENTIALS',
        statusCode: 400
      });
    });

    it('should handle database errors gracefully', async () => {
      mockUserModel.verifyPasswordByUsername.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        });

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        message: 'Internal server error during login',
        code: 'LOGIN_ERROR',
        statusCode: 500
      });
    });
  });

  describe('GET /api/auth/profile', () => {
    const validUser: User = {
      id: 1,
      name: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('should return user profile with valid JWT', async () => {
      const token = generateToken(validUser);
      mockUserModel.findById.mockResolvedValue(validUser);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.user).toEqual({
        id: validUser.id,
        username: validUser.name,
        role: validUser.role,
        email: validUser.email,
        createdAt: validUser.createdAt.toISOString(),
        updatedAt: validUser.updatedAt.toISOString()
      });
      expect(mockUserModel.findById).toHaveBeenCalledWith(validUser.id);
    });

    it('should return 401 for missing Authorization header', async () => {
      const response = await request(app)
        .get('/api/auth/profile');

      expect(response.status).toBe(401);
      expect(response.body.error).toEqual({
        message: 'Access denied. No token provided.',
        code: 'NO_TOKEN',
        statusCode: 401
      });
    });

    it('should return 401 for malformed Authorization header', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'InvalidToken');

      expect(response.status).toBe(401);
      expect(response.body.error).toEqual({
        message: 'Access denied. No token provided.',
        code: 'NO_TOKEN',
        statusCode: 401
      });
    });

    it('should return 401 for invalid JWT token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid.jwt.token');

      expect(response.status).toBe(401);
      expect(response.body.error).toEqual({
        message: 'Access denied. Invalid token.',
        code: 'INVALID_TOKEN',
        statusCode: 401
      });
    });

    it('should return 404 if user not found in database', async () => {
      const token = generateToken(validUser);
      mockUserModel.findById.mockResolvedValue(null);

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(404);
      expect(response.body.error).toEqual({
        message: 'User not found',
        code: 'USER_NOT_FOUND',
        statusCode: 404
      });
    });

    it('should handle database errors gracefully', async () => {
      const token = generateToken(validUser);
      mockUserModel.findById.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        message: 'Internal server error during profile retrieval',
        code: 'PROFILE_ERROR',
        statusCode: 500
      });
    });
  });
});
