import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { UserManagementPage } from '../pages/Admin/UserManagementPage';
import { AuthProvider } from '../store/authStore';
import * as authService from '../services/authService';

// Mock the auth service
jest.mock('../services/authService');
const mockAuthService = authService as jest.Mocked<typeof authService>;

// Mock the useAuth hook to provide stable references
const mockUser = {
  id: 1,
  name: 'admin',
  email: '<EMAIL>',
  role: 'admin' as const,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
};

const mockUsers = [
  mockUser,
  {
    id: 2,
    name: 'testuser',
    email: '<EMAIL>',
    role: 'user' as const,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }
];

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      {children}
    </AuthProvider>
  </BrowserRouter>
);

describe('UserManagementPage Infinite Rendering Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock localStorage to return admin user
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'teleai_jwt_token') return 'mock-jwt-token';
      if (key === 'teleai_user') return JSON.stringify(mockUser);
      return null;
    });

    // Mock auth service methods
    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getProfile.mockResolvedValue(mockUser);
    mockAuthService.getUsers.mockResolvedValue({
      users: mockUsers,
      count: mockUsers.length
    });
    mockAuthService.createUser.mockResolvedValue({
      id: 3,
      name: 'newuser',
      email: '<EMAIL>',
      role: 'user',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    });
  });

  it('should render without infinite loops', async () => {
    // Track how many times getUsers is called
    let getUsersCallCount = 0;
    mockAuthService.getUsers.mockImplementation(async () => {
      getUsersCallCount++;
      return {
        users: mockUsers,
        count: mockUsers.length
      };
    });

    render(
      <TestWrapper>
        <UserManagementPage />
      </TestWrapper>
    );

    // Wait for initial render and data loading
    await waitFor(() => {
      expect(screen.getByText('User Management')).toBeInTheDocument();
    });

    // Wait a bit more to ensure no additional calls are made
    await new Promise(resolve => setTimeout(resolve, 1000));

    // getUsers should only be called once for initial load
    expect(getUsersCallCount).toBeLessThanOrEqual(2); // Allow for one retry
  });

  it('should refresh user list when new user is created', async () => {
    let getUsersCallCount = 0;
    mockAuthService.getUsers.mockImplementation(async () => {
      getUsersCallCount++;
      return {
        users: mockUsers,
        count: mockUsers.length
      };
    });

    render(
      <TestWrapper>
        <UserManagementPage />
      </TestWrapper>
    );

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText('User Management')).toBeInTheDocument();
    });

    const initialCallCount = getUsersCallCount;

    // Find and fill the user creation form
    const usernameInput = screen.getByPlaceholderText('Enter username (min 3 characters)');
    const passwordInput = screen.getByPlaceholderText('Enter password (min 6 characters)');
    const createButton = screen.getByRole('button', { name: /create user/i });

    fireEvent.change(usernameInput, { target: { value: 'newuser' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(createButton);

    // Wait for user creation and list refresh
    await waitFor(() => {
      expect(getUsersCallCount).toBeGreaterThan(initialCallCount);
    });

    // Should not continue calling getUsers infinitely
    await new Promise(resolve => setTimeout(resolve, 1000));
    const finalCallCount = getUsersCallCount;
    
    // Allow some buffer for legitimate calls but ensure it's not infinite
    expect(finalCallCount).toBeLessThan(initialCallCount + 5);
  });

  it('should handle search and filtering without infinite renders', async () => {
    let getUsersCallCount = 0;
    mockAuthService.getUsers.mockImplementation(async () => {
      getUsersCallCount++;
      return {
        users: mockUsers,
        count: mockUsers.length
      };
    });

    render(
      <TestWrapper>
        <UserManagementPage />
      </TestWrapper>
    );

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText('User Management')).toBeInTheDocument();
    });

    const initialCallCount = getUsersCallCount;

    // Find and use search input
    const searchInput = screen.getByPlaceholderText('Search users...');
    fireEvent.change(searchInput, { target: { value: 'admin' } });

    // Wait a bit to ensure no additional API calls are made for filtering
    await new Promise(resolve => setTimeout(resolve, 500));

    // Filtering should not trigger additional API calls
    expect(getUsersCallCount).toBe(initialCallCount);
  });

  it('should handle role filtering without infinite renders', async () => {
    let getUsersCallCount = 0;
    mockAuthService.getUsers.mockImplementation(async () => {
      getUsersCallCount++;
      return {
        users: mockUsers,
        count: mockUsers.length
      };
    });

    render(
      <TestWrapper>
        <UserManagementPage />
      </TestWrapper>
    );

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText('User Management')).toBeInTheDocument();
    });

    const initialCallCount = getUsersCallCount;

    // Find and use role filter
    const roleFilter = screen.getByDisplayValue('All Roles');
    fireEvent.change(roleFilter, { target: { value: 'admin' } });

    // Wait a bit to ensure no additional API calls are made for filtering
    await new Promise(resolve => setTimeout(resolve, 500));

    // Filtering should not trigger additional API calls
    expect(getUsersCallCount).toBe(initialCallCount);
  });
});
