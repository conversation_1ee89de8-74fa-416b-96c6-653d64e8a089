import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { User, AuthState, CreateUserRequest } from '../types';
import authService, { ChangePasswordRequest, AdminResetPasswordRequest } from '../services/authService';

const AuthContext = createContext<AuthState | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check for existing session on mount
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      try {
        // Check if user is authenticated with valid token
        if (authService.isAuthenticated()) {
          // Try to get fresh user data from API
          const userData = await authService.verifyAndRefreshUser();
          if (userData) {
            setUser(userData);
            setIsAuthenticated(true);
          } else {
            // Token invalid or expired
            setUser(null);
            setIsAuthenticated(false);
          }
        } else {
          // No valid token found
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        setUser(null);
        setIsAuthenticated(false);
        authService.logout(); // Clean up any invalid tokens
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login({ username, password });

      setUser(response.user);
      setIsAuthenticated(true);

      // Save user data for offline access
      localStorage.setItem('teleai_user', JSON.stringify(response.user));

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setError(errorMessage);
      setUser(null);
      setIsAuthenticated(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setUser(null);
    setIsAuthenticated(false);
    setError(null);
  };

  const getProfile = useCallback(async (): Promise<User | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const userData = await authService.getProfile();
      setUser(userData);
      localStorage.setItem('teleai_user', JSON.stringify(userData));
      return userData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get profile';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createUser = useCallback(async (userData: CreateUserRequest): Promise<User> => {
    setIsLoading(true);
    setError(null);

    try {
      const newUser = await authService.createUser(userData);
      return newUser;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create user';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getUsers = useCallback(async (): Promise<{ users: User[]; count: number }> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.getUsers();
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get users';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const changePassword = useCallback(async (currentPassword: string, newPassword: string): Promise<User> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.changePassword({ currentPassword, newPassword });

      // Update user data in state
      setUser(response.user);
      localStorage.setItem('teleai_user', JSON.stringify(response.user));

      return response.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change password';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const adminResetPassword = useCallback(async (username: string, newPassword: string): Promise<User> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.adminResetPassword({ username, newPassword });
      return response.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset password';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const value: AuthState = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    getProfile,
    createUser,
    getUsers,
    changePassword,
    adminResetPassword,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
