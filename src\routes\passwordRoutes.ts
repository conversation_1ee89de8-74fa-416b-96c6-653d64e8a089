import { Router } from 'express';
import { PasswordController } from '../controllers/passwordController';
import { authMiddleware } from '../middleware/authMiddleware';
import { isAdminMiddleware } from '../middleware/isAdminMiddleware';
import logger from '../utils/logger';

const router = Router();

/**
 * Password Management Routes
 * All routes require authentication
 */

/**
 * PUT /api/auth/change-password
 * User endpoint to change their own password
 * Requires: Valid JWT token + current password verification
 * Body: { currentPassword: string, newPassword: string }
 */
router.put('/change-password', authMiddleware, (req, res, next) => {
  logger.info('Password change request', {
    userId: (req as any).user?.id,
    username: (req as any).user?.username,
    ip: req.ip
  });
  
  PasswordController.changePassword(req, res).catch(next);
});

/**
 * PUT /api/admin/reset-password
 * Admin endpoint to reset any user's password
 * Requires: Valid <PERSON> token with admin role
 * Body: { username: string, newPassword: string }
 */
router.put('/reset-password', authMiddleware, isAdminMiddleware, (req, res, next) => {
  logger.info('Admin password reset request', {
    targetUsername: req.body.username,
    adminId: (req as any).user?.id,
    adminUsername: (req as any).user?.username,
    ip: req.ip
  });
  
  PasswordController.adminResetPassword(req, res).catch(next);
});

export default router;
