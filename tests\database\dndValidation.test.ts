import { DNDValidationModel } from '../../src/database/models/DNDValidation';
import { db } from '../../src/database/connection';
import { CreateDNDValidationRequest } from '../../src/types';

describe('DNDValidationModel', () => {
  let model: DNDValidationModel;

  beforeAll(async () => {
    await db.connect();
    model = new DNDValidationModel();
  });

  afterAll(async () => {
    // Clean up test data
    try {
      await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['test%']);
    } catch (error) {
      console.log('Cleanup error:', error);
    }
    await db.disconnect();
  });

  beforeEach(async () => {
    // Clear any existing test data
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['test%']);
  });

  describe('basic functionality', () => {
    it('should create a new DND validation record', async () => {
      const validationData: CreateDNDValidationRequest = {
        phoneNumber: 'test9876543210',
        dndStatus: 'DND',
        validationMetadata: {
          source: 'test',
          originalPhone: '+919876543210'
        }
      };

      const result = await model.upsert(validationData);

      expect(result.phoneNumber).toBe('test9876543210');
      expect(result.dndStatus).toBe('DND');
      expect(result.validationMetadata).toEqual(validationData.validationMetadata);
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('should find existing DND validation by phone number', async () => {
      const phoneNumber = 'test9876543211';
      const validationData: CreateDNDValidationRequest = {
        phoneNumber,
        dndStatus: 'Non-DND',
        validationMetadata: { test: 'data' }
      };

      await model.upsert(validationData);
      const found = await model.findByPhone(phoneNumber);

      expect(found).not.toBeNull();
      expect(found!.phoneNumber).toBe(phoneNumber);
      expect(found!.dndStatus).toBe('Non-DND');
      expect(found!.validationMetadata).toEqual({ test: 'data' });
    });

    it('should return null for non-existent phone number', async () => {
      const found = await model.findByPhone('9999999999'); // Valid format but non-existent
      expect(found).toBeNull();
    });
  });
});
