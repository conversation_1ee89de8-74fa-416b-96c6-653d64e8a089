-- Enhance users table for JWT authentication system
-- Created: 2024-12-23
-- Purpose: Add necessary indexes and constraints for username-based authentication

-- Add unique constraint on name field (username)
-- This prevents duplicate usernames at the database level
ALTER TABLE users ADD CONSTRAINT users_name_unique UNIQUE (name);

-- Add index on name field for performance (username lookups)
CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);

-- Add index on role field for role-based queries
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- Add composite index for email and name lookups
CREATE INDEX IF NOT EXISTS idx_users_email_name ON users(email, name);

-- Update the existing admin user to use the new username-based system
-- First, check if the old admin user exists and update it
DO $$
BEGIN
    -- Update existing admin user to have a proper username
    UPDATE users 
    SET name = 'admin', 
        email = '<EMAIL>'
    WHERE email = '<EMAIL>' 
    AND role = 'admin';
    
    -- If no admin user exists, create one
    IF NOT EXISTS (SELECT 1 FROM users WHERE role = 'admin') THEN
        INSERT INTO users (email, name, password_hash, role) 
        VALUES (
            '<EMAIL>', 
            'admin', 
            '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
            'admin'
        );
    END IF;
END $$;

-- Add comments for documentation
COMMENT ON TABLE users IS 'User accounts with JWT authentication support';
COMMENT ON COLUMN users.id IS 'Primary key - auto-incrementing user ID';
COMMENT ON COLUMN users.email IS 'User email address - must be unique';
COMMENT ON COLUMN users.name IS 'Username for authentication - must be unique';
COMMENT ON COLUMN users.password_hash IS 'bcrypt hashed password (10 salt rounds)';
COMMENT ON COLUMN users.role IS 'User role: admin (full access) or user (standard access)';
COMMENT ON COLUMN users.created_at IS 'Account creation timestamp';
COMMENT ON COLUMN users.updated_at IS 'Last account update timestamp (auto-updated)';

-- Add constraint to ensure name is not empty and has minimum length
ALTER TABLE users ADD CONSTRAINT users_name_length_check 
    CHECK (length(trim(name)) >= 3);

-- Add constraint to ensure email format is valid (basic check)
ALTER TABLE users ADD CONSTRAINT users_email_format_check 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Add constraint to ensure password_hash is not empty
ALTER TABLE users ADD CONSTRAINT users_password_hash_not_empty_check 
    CHECK (length(password_hash) > 0);

-- Create function to validate username format
CREATE OR REPLACE FUNCTION validate_username(username TEXT) 
RETURNS BOOLEAN AS $$
BEGIN
    -- Username must be 3-50 characters, alphanumeric plus underscore and hyphen
    RETURN username ~ '^[a-zA-Z0-9_-]{3,50}$';
END;
$$ LANGUAGE plpgsql;

-- Add constraint to validate username format
ALTER TABLE users ADD CONSTRAINT users_name_format_check 
    CHECK (validate_username(name));

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_updated_at ON users(updated_at);

-- Create partial index for active admin users (performance optimization)
CREATE INDEX IF NOT EXISTS idx_users_active_admins ON users(id) 
    WHERE role = 'admin';

-- Add database-level security: ensure at least one admin user always exists
CREATE OR REPLACE FUNCTION ensure_admin_exists() 
RETURNS TRIGGER AS $$
BEGIN
    -- If trying to delete or update the last admin user, prevent it
    IF (TG_OP = 'DELETE' OR (TG_OP = 'UPDATE' AND NEW.role != 'admin')) 
       AND OLD.role = 'admin' THEN
        
        -- Check if this would leave no admin users
        IF (SELECT COUNT(*) FROM users WHERE role = 'admin' AND id != OLD.id) = 0 THEN
            RAISE EXCEPTION 'Cannot delete or demote the last admin user. At least one admin must exist.';
        END IF;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to ensure at least one admin always exists
CREATE TRIGGER ensure_admin_exists_trigger
    BEFORE UPDATE OR DELETE ON users
    FOR EACH ROW
    EXECUTE FUNCTION ensure_admin_exists();

-- Create function to normalize usernames (lowercase, trim)
CREATE OR REPLACE FUNCTION normalize_username() 
RETURNS TRIGGER AS $$
BEGIN
    -- Normalize username: trim whitespace and convert to lowercase
    NEW.name = lower(trim(NEW.name));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to normalize usernames on insert/update
CREATE TRIGGER normalize_username_trigger
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION normalize_username();

-- Performance optimization: analyze table after changes
ANALYZE users;
