/**
 * @fileoverview Custom Error Classes for DND Persistence Operations
 * @description Comprehensive error hierarchy for DND validation persistence with proper error handling
 * <AUTHOR> System
 * @version 1.0.0
 */

import { DND_PERSISTENCE_ERROR_CODES } from '../config/dndPersistence';

/**
 * Base error class for all DND persistence operations
 * @extends Error
 */
export abstract class DNDPersistenceError extends Error {
  /**
   * Error code for programmatic error handling
   */
  public code: string;

  /**
   * HTTP status code for API responses
   */
  public statusCode: number;

  /**
   * Additional context data for debugging
   */
  public context?: Record<string, any>;

  /**
   * Timestamp when the error occurred
   */
  public readonly timestamp: Date;

  /**
   * Whether this error should be retried
   */
  public retryable: boolean;

  /**
   * Creates a new DNDPersistenceError
   * @param {string} message - Human-readable error message
   * @param {string} code - Error code for programmatic handling
   * @param {number} statusCode - HTTP status code
   * @param {Record<string, any>} context - Additional context data
   * @param {boolean} retryable - Whether this error should be retried
   */
  constructor(
    message: string,
    code: string,
    statusCode: number = 500,
    context?: Record<string, any>,
    retryable: boolean = false
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
    this.context = context || {};
    this.timestamp = new Date();
    this.retryable = retryable;
    
    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Convert error to JSON for logging and API responses
   * @returns {object} JSON representation of the error
   */
  toJSON(): object {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      context: this.context,
      timestamp: this.timestamp.toISOString(),
      retryable: this.retryable,
      stack: this.stack
    };
  }

  /**
   * Get sanitized error for client responses (removes sensitive data)
   * @returns {object} Sanitized error object
   */
  toClientError(): object {
    return {
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      timestamp: this.timestamp.toISOString()
    };
  }
}

/**
 * Validation errors for input data
 * @extends DNDPersistenceError
 */
export class DNDValidationError extends DNDPersistenceError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, DND_PERSISTENCE_ERROR_CODES.INVALID_PHONE_NUMBER, 400, context, false);
  }
}

/**
 * Invalid phone number format error
 * @extends DNDValidationError
 */
export class InvalidPhoneNumberError extends DNDValidationError {
  constructor(phoneNumber: string, reason?: string) {
    const message = `Invalid phone number format: ${phoneNumber}${reason ? ` (${reason})` : ''}`;
    super(message, { phoneNumber, reason });
    this.code = DND_PERSISTENCE_ERROR_CODES.INVALID_PHONE_NUMBER;
  }
}

/**
 * Invalid DND status error
 * @extends DNDValidationError
 */
export class InvalidDNDStatusError extends DNDValidationError {
  constructor(status: string, validStatuses: string[]) {
    const message = `Invalid DND status: ${status}. Valid statuses: ${validStatuses.join(', ')}`;
    super(message, { status, validStatuses });
    this.code = DND_PERSISTENCE_ERROR_CODES.INVALID_DND_STATUS;
  }
}

/**
 * Invalid metadata error
 * @extends DNDValidationError
 */
export class InvalidMetadataError extends DNDValidationError {
  constructor(reason: string, metadata?: any) {
    const message = `Invalid validation metadata: ${reason}`;
    super(message, { reason, metadata });
    this.code = DND_PERSISTENCE_ERROR_CODES.INVALID_METADATA;
  }
}

/**
 * Database operation errors
 * @extends DNDPersistenceError
 */
export class DNDDatabaseError extends DNDPersistenceError {
  constructor(message: string, code: string, context?: Record<string, any>, retryable: boolean = true) {
    super(message, code, 500, context, retryable);
  }
}

/**
 * Database connection error
 * @extends DNDDatabaseError
 */
export class DatabaseConnectionError extends DNDDatabaseError {
  constructor(originalError?: Error) {
    const message = 'Failed to connect to database';
    super(message, DND_PERSISTENCE_ERROR_CODES.CONNECTION_FAILED, { originalError: originalError?.message }, true);
  }
}

/**
 * Database query timeout error
 * @extends DNDDatabaseError
 */
export class QueryTimeoutError extends DNDDatabaseError {
  constructor(query: string, timeoutMs: number) {
    const message = `Database query timed out after ${timeoutMs}ms`;
    super(message, DND_PERSISTENCE_ERROR_CODES.QUERY_TIMEOUT, { query, timeoutMs }, true);
  }
}

/**
 * Database transaction error
 * @extends DNDDatabaseError
 */
export class TransactionError extends DNDDatabaseError {
  constructor(operation: string, originalError?: Error) {
    const message = `Transaction failed during ${operation}`;
    super(message, DND_PERSISTENCE_ERROR_CODES.TRANSACTION_FAILED, { 
      operation, 
      originalError: originalError?.message 
    }, false);
  }
}

/**
 * Business logic errors
 * @extends DNDPersistenceError
 */
export class DNDBusinessLogicError extends DNDPersistenceError {
  constructor(message: string, code: string, context?: Record<string, any>) {
    super(message, code, 409, context, false);
  }
}

/**
 * Validation not found error
 * @extends DNDBusinessLogicError
 */
export class ValidationNotFoundError extends DNDBusinessLogicError {
  constructor(phoneNumber: string) {
    const message = `DND validation not found for phone number: ${phoneNumber}`;
    super(message, DND_PERSISTENCE_ERROR_CODES.VALIDATION_NOT_FOUND, { phoneNumber });
    this.statusCode = 404;
  }
}

/**
 * Recent validation exists error
 * @extends DNDBusinessLogicError
 */
export class RecentValidationExistsError extends DNDBusinessLogicError {
  constructor(phoneNumber: string, lastValidatedAt: Date, withinHours: number) {
    const message = `Recent validation exists for ${phoneNumber}, validated ${withinHours} hours ago`;
    super(message, DND_PERSISTENCE_ERROR_CODES.RECENT_VALIDATION_EXISTS, { 
      phoneNumber, 
      lastValidatedAt: lastValidatedAt.toISOString(), 
      withinHours 
    });
  }
}

/**
 * Rate limiting errors
 * @extends DNDPersistenceError
 */
export class RateLimitError extends DNDPersistenceError {
  constructor(limit: number, timeWindow: string, currentCount: number) {
    const message = `Rate limit exceeded: ${currentCount}/${limit} requests per ${timeWindow}`;
    super(message, DND_PERSISTENCE_ERROR_CODES.RATE_LIMIT_EXCEEDED, 429, { 
      limit, 
      timeWindow, 
      currentCount 
    }, true);
  }
}

/**
 * Bulk operation size exceeded error
 * @extends DNDPersistenceError
 */
export class BulkSizeExceededError extends DNDPersistenceError {
  constructor(actualSize: number, maxSize: number) {
    const message = `Bulk operation size exceeded: ${actualSize} > ${maxSize}`;
    super(message, DND_PERSISTENCE_ERROR_CODES.BULK_SIZE_EXCEEDED, 413, { 
      actualSize, 
      maxSize 
    }, false);
  }
}

/**
 * Configuration error
 * @extends DNDPersistenceError
 */
export class ConfigurationError extends DNDPersistenceError {
  constructor(setting: string, value: any, reason: string) {
    const message = `Invalid configuration for ${setting}: ${reason}`;
    super(message, DND_PERSISTENCE_ERROR_CODES.CONFIGURATION_ERROR, 500, { 
      setting, 
      value, 
      reason 
    }, false);
  }
}

/**
 * Internal system error
 * @extends DNDPersistenceError
 */
export class InternalError extends DNDPersistenceError {
  constructor(operation: string, originalError?: Error) {
    const message = `Internal error during ${operation}`;
    super(message, DND_PERSISTENCE_ERROR_CODES.INTERNAL_ERROR, 500, { 
      operation, 
      originalError: originalError?.message,
      stack: originalError?.stack
    }, false);
  }
}

/**
 * Error factory for creating appropriate error instances
 */
export class DNDErrorFactory {
  /**
   * Create error from database operation failure
   * @param {Error} originalError - The original database error
   * @param {string} operation - The operation that failed
   * @param {Record<string, any>} context - Additional context
   * @returns {DNDPersistenceError} Appropriate error instance
   */
  static fromDatabaseError(
    originalError: Error, 
    operation: string, 
    context?: Record<string, any>
  ): DNDPersistenceError {
    const errorMessage = originalError.message.toLowerCase();
    
    if (errorMessage.includes('timeout')) {
      return new QueryTimeoutError(operation, 30000);
    }
    
    if (errorMessage.includes('connection') || errorMessage.includes('connect')) {
      return new DatabaseConnectionError(originalError);
    }
    
    if (errorMessage.includes('transaction')) {
      return new TransactionError(operation, originalError);
    }
    
    return new DNDDatabaseError(
      `Database operation failed: ${originalError.message}`,
      DND_PERSISTENCE_ERROR_CODES.INTERNAL_ERROR,
      { ...context, originalError: originalError.message },
      true
    );
  }

  /**
   * Create validation error from input validation failure
   * @param {string} field - The field that failed validation
   * @param {any} value - The invalid value
   * @param {string} reason - The reason for validation failure
   * @returns {DNDValidationError} Appropriate validation error
   */
  static fromValidationFailure(field: string, value: any, reason: string): DNDValidationError {
    switch (field) {
      case 'phoneNumber':
        return new InvalidPhoneNumberError(value, reason);
      case 'dndStatus':
        return new InvalidDNDStatusError(value, ['DND', 'Non-DND', 'Error']);
      case 'metadata':
        return new InvalidMetadataError(reason, value);
      default:
        return new DNDValidationError(`Invalid ${field}: ${reason}`, { field, value, reason });
    }
  }
}

/**
 * Type guard to check if an error is a DND persistence error
 * @param {any} error - The error to check
 * @returns {boolean} True if the error is a DND persistence error
 */
export function isDNDPersistenceError(error: any): error is DNDPersistenceError {
  return error instanceof DNDPersistenceError;
}

/**
 * Type guard to check if an error is retryable
 * @param {any} error - The error to check
 * @returns {boolean} True if the error is retryable
 */
export function isRetryableError(error: any): boolean {
  return isDNDPersistenceError(error) && error.retryable;
}
