import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { authMiddleware } from '../middleware/authMiddleware';
import { isAdminMiddleware } from '../middleware/isAdminMiddleware';
import logger from '../utils/logger';

const router = Router();

/**
 * User Management Routes
 * Base path: /api/users
 * All routes require authentication and admin privileges
 */

/**
 * POST /api/users
 * Admin-only endpoint to create new users
 * Requires: Valid JWT token with admin role
 * Accepts: { username: string, password: string, role?: 'admin' | 'user' }
 * Returns: { user: object }
 */
router.post('/', authMiddleware, isAdminMiddleware, (req, res, next) => {
  logger.info('User creation request', {
    username: req.body.username,
    role: req.body.role,
    adminId: (req as any).user?.id,
    adminUsername: (req as any).user?.username,
    ip: req.ip
  });
  
  UserController.createUser(req, res).catch(next);
});

/**
 * GET /api/users
 * Admin-only endpoint to get all users
 * Requires: Valid JWT token with admin role
 * Returns: { users: array, count: number }
 */
router.get('/', authMiddleware, isAdminMiddleware, (req, res, next) => {
  logger.info('Users list request', {
    adminId: (req as any).user?.id,
    adminUsername: (req as any).user?.username,
    ip: req.ip
  });
  
  UserController.getAllUsers(req, res).catch(next);
});

export default router;
