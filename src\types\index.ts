// Core entity types
export interface User {
  id: number;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
}

export interface Lead {
  id: number;
  name: string;
  phone: string;
  email?: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'rejected';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Call {
  id: number;
  leadId: number;
  twilioCallSid?: string;
  status: 'initiated' | 'ringing' | 'in-progress' | 'completed' | 'failed' | 'no-answer';
  duration?: number; // in seconds
  recordingUrl?: string;
  transcript?: string;
  aiResponse?: string;
  startedAt?: Date;
  endedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// API Request/Response types
export interface CreateLeadRequest {
  name: string;
  phone: string;
  email?: string;
  notes?: string;
}

export interface UpdateLeadRequest {
  name?: string;
  phone?: string;
  email?: string;
  status?: Lead['status'];
  notes?: string;
}

export interface CreateUserRequest {
  email: string;
  name: string;
  password: string;
  role?: User['role'];
}

export interface UpdateUserRequest {
  email?: string;
  name?: string;
  password?: string;
  role?: User['role'];
}

export interface CreateUserByUsernameRequest {
  username: string;
  password: string;
  role?: User['role'];
}

export interface UpdatePasswordRequest {
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface AdminUpdatePasswordRequest {
  username: string;
  newPassword: string;
}

export interface InitiateCallRequest {
  leadId: number;
  message?: string;
}

export interface AuthRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: Omit<User, 'password'>;
}

// Database types
export interface QueryResult {
  rows: any[];
  rowCount: number;
}

// Service interfaces
export interface DatabaseService {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  query(text: string, params?: any[]): Promise<QueryResult>;
  transaction<T>(callback: (query: (sql: string, params?: any[]) => Promise<QueryResult>) => Promise<T>): Promise<T>;
  healthCheck(): Promise<boolean>;
}

export interface TelephonyService {
  initiateCall(phoneNumber: string, message: string): Promise<string>;
  getCallStatus(callSid: string): Promise<string>;
  getRecording(callSid: string): Promise<string | null>;
  validatePhoneNumber(phone: string): boolean;
  endCall?(callSid: string): Promise<void>;
}

export interface AIService {
  processVoiceInput(audioData: Buffer): Promise<string>;
  generateResponse(transcript: string, context: string): Promise<string>;
  synthesizeSpeech(text: string): Promise<Buffer>;
}

// Configuration types
export interface Config {
  port: number;
  nodeEnv: string;
  database: {
    url: string;
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  twilio: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
  };
  litellm: {
    baseUrl: string;
    apiKey: string;
  };
  openai?: {
    apiKey: string;
  };
}

// Error types
export interface ApiError extends Error {
  statusCode: number;
  code?: string;
}

// Express middleware types
export interface AuthenticatedRequest extends Express.Request {
  user?: User;
}

// Webhook types
export interface TwilioWebhookRequest {
  CallSid: string;
  CallStatus: string;
  From: string;
  To: string;
  Duration?: string;
  RecordingUrl?: string;
}

// DND Validation types
export interface DNDValidation {
  phoneNumber: string;
  dndStatus: 'DND' | 'Non-DND' | 'Error';
  validatedAt: Date;
  validationMetadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateDNDValidationRequest {
  phoneNumber: string;
  dndStatus: 'DND' | 'Non-DND' | 'Error';
  validatedAt?: Date;
  validationMetadata?: Record<string, any>;
}

export interface UpdateDNDValidationRequest {
  dndStatus?: 'DND' | 'Non-DND' | 'Error';
  validatedAt?: Date;
  validationMetadata?: Record<string, any>;
}

export interface DNDValidationStats {
  totalValidations: number;
  dndCount: number;
  nonDndCount: number;
  errorCount: number;
  lastValidatedAt?: Date;
}

// DND Persistence Configuration
export interface DNDPersistenceConfig {
  defaultBatchSize: number;
  maxBatchSize: number;
  retryAttempts: number;
  retryDelayMs: number;
  connectionTimeout: number;
  queryTimeout: number;
  recentValidationHours: number;
  auditLogging: boolean;
  performanceMonitoring: boolean;
  rateLimiting: {
    enabled: boolean;
    maxRequestsPerMinute: number;
    maxRequestsPerHour: number;
  };
  cleanup: {
    enabled: boolean;
    retentionDays: number;
    batchSize: number;
  };
}

// DND Persistence Options
export interface DNDPersistenceOptions {
  skipRecentValidations?: boolean;
  recentValidationHours?: number;
  includeMetadata?: boolean;
  enableAuditLog?: boolean;
  transactionId?: string;
  userId?: string;
  source?: string;
}

// DND Persistence Interfaces for Dependency Injection
export interface IDNDValidationRepository {
  findByPhone(phoneNumber: string): Promise<DNDValidation | null>;
  findAll(options: DNDValidationQueryOptions): Promise<DNDValidationQueryResult>;
  upsert(validationData: CreateDNDValidationRequest): Promise<DNDValidation>;
  bulkUpsert(validations: CreateDNDValidationRequest[]): Promise<DNDValidation[]>;
  update(phoneNumber: string, updateData: UpdateDNDValidationRequest): Promise<DNDValidation>;
  delete(phoneNumber: string): Promise<void>;
  getStats(): Promise<DNDValidationStats>;
  isRecentlyValidated(phoneNumber: string, withinHours: number): Promise<boolean>;
  healthCheck(): Promise<DNDHealthCheckResult>;
}

export interface IDNDPersistenceService {
  saveDNDValidation(result: any, options?: DNDPersistenceOptions): Promise<DNDValidation>;
  saveBulkDNDValidations(results: any[], options?: DNDPersistenceOptions): Promise<DNDValidation[]>;
  getDNDValidation(phoneNumber: string): Promise<DNDValidation | null>;
  isRecentlyValidated(phoneNumber: string, withinHours?: number): Promise<boolean>;
  getValidationStats(): Promise<DNDValidationStats>;
  getDNDValidations(options?: DNDValidationQueryOptions): Promise<DNDValidationQueryResult>;
  deleteDNDValidation(phoneNumber: string): Promise<void>;
  cleanupOldValidations(olderThanDays?: number): Promise<number>;
}

export interface IDNDAuditLogger {
  logEvent(event: string, data: Record<string, any>, userId?: string): Promise<void>;
  logValidationCreated(validation: DNDValidation, userId?: string): Promise<void>;
  logValidationUpdated(validation: DNDValidation, userId?: string): Promise<void>;
  logValidationDeleted(phoneNumber: string, userId?: string): Promise<void>;
  logBulkOperation(operation: string, count: number, userId?: string): Promise<void>;
  logCleanupOperation(deletedCount: number, retentionDays: number, userId?: string): Promise<void>;
}

export interface IDNDValidator {
  validatePhoneNumber(phoneNumber: string): ValidationResult;
  validateDNDStatus(status: string): ValidationResult;
  validateMetadata(metadata: any): ValidationResult;
  validateBulkSize(size: number, maxSize: number): ValidationResult;
  validatePagination(page: number, limit: number): ValidationResult;
  sanitizePhoneNumber(phoneNumber: string): string;
  sanitizeMetadata(metadata: any): Record<string, any>;
}

// Supporting interfaces
export interface DNDValidationQueryOptions {
  page?: number;
  limit?: number;
  dndStatus?: 'DND' | 'Non-DND' | 'Error';
  search?: string;
  startDate?: Date;
  endDate?: Date;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface DNDValidationQueryResult {
  validations: DNDValidation[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitized?: any;
}

export interface DNDHealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  totalRecords: number;
  recentRecords: number;
  databaseSize: number;
  responseTime: number;
  errors?: string[];
}

export interface DNDAuditLogEntry {
  id: string;
  event: string;
  phoneNumber?: string;
  userId?: string;
  data: Record<string, any>;
  timestamp: Date;
  source: string;
}

// Test types
export interface TestContext {
  app: Express.Application;
  db: DatabaseService;
  cleanup: () => Promise<void>;
}
