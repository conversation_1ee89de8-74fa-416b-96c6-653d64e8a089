#!/usr/bin/env tsx

/**
 * Database schema verification script for JWT authentication system
 * Verifies that all required tables, indexes, constraints, and triggers are properly set up
 */

import { db } from '../src/database/connection';

interface SchemaCheck {
  name: string;
  type: 'table' | 'column' | 'index' | 'constraint' | 'trigger' | 'function';
  status: 'pass' | 'fail' | 'warning';
  message: string;
  query?: string;
  expected?: any;
  actual?: any;
}

const checks: Schema<PERSON>he<PERSON>[] = [];

function addCheck(
  name: string, 
  type: Schem<PERSON><PERSON>heck['type'], 
  status: SchemaCheck['status'], 
  message: string,
  query?: string,
  expected?: any,
  actual?: any
) {
  checks.push({ name, type, status, message, query, expected, actual });
}

async function checkTableExists(tableName: string): Promise<boolean> {
  const result = await db.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = $1
    );
  `, [tableName]);
  
  return result.rows[0].exists;
}

async function checkColumnExists(tableName: string, columnName: string): Promise<any> {
  const result = await db.query(`
    SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = $1 
    AND column_name = $2;
  `, [tableName, columnName]);
  
  return result.rows[0] || null;
}

async function checkIndexExists(indexName: string): Promise<boolean> {
  const result = await db.query(`
    SELECT EXISTS (
      SELECT FROM pg_indexes 
      WHERE schemaname = 'public' 
      AND indexname = $1
    );
  `, [indexName]);
  
  return result.rows[0].exists;
}

async function checkConstraintExists(tableName: string, constraintName: string): Promise<any> {
  const result = await db.query(`
    SELECT constraint_name, constraint_type
    FROM information_schema.table_constraints 
    WHERE table_schema = 'public' 
    AND table_name = $1 
    AND constraint_name = $2;
  `, [tableName, constraintName]);
  
  return result.rows[0] || null;
}

async function checkTriggerExists(triggerName: string, tableName: string): Promise<boolean> {
  const result = await db.query(`
    SELECT EXISTS (
      SELECT FROM information_schema.triggers 
      WHERE event_object_schema = 'public' 
      AND event_object_table = $1 
      AND trigger_name = $2
    );
  `, [tableName, triggerName]);
  
  return result.rows[0].exists;
}

async function checkFunctionExists(functionName: string): Promise<boolean> {
  const result = await db.query(`
    SELECT EXISTS (
      SELECT FROM pg_proc 
      WHERE proname = $1
    );
  `, [functionName]);
  
  return result.rows[0].exists;
}

async function verifyUsersTable(): Promise<void> {
  console.log('🔍 Verifying users table structure...\n');

  // Check if users table exists
  const tableExists = await checkTableExists('users');
  if (tableExists) {
    addCheck('Users Table', 'table', 'pass', 'Users table exists');
  } else {
    addCheck('Users Table', 'table', 'fail', 'Users table does not exist');
    return;
  }

  // Check required columns
  const requiredColumns = [
    { name: 'id', type: 'integer', nullable: 'NO' },
    { name: 'email', type: 'character varying', nullable: 'NO' },
    { name: 'name', type: 'character varying', nullable: 'NO' },
    { name: 'password_hash', type: 'character varying', nullable: 'NO' },
    { name: 'role', type: 'character varying', nullable: 'YES' },
    { name: 'created_at', type: 'timestamp with time zone', nullable: 'YES' },
    { name: 'updated_at', type: 'timestamp with time zone', nullable: 'YES' }
  ];

  for (const col of requiredColumns) {
    const column = await checkColumnExists('users', col.name);
    if (column) {
      if (column.data_type === col.type && column.is_nullable === col.nullable) {
        addCheck(`Column: ${col.name}`, 'column', 'pass', 
          `Column ${col.name} exists with correct type (${col.type})`);
      } else {
        addCheck(`Column: ${col.name}`, 'column', 'warning', 
          `Column ${col.name} exists but type/nullable mismatch`, 
          '', col, column);
      }
    } else {
      addCheck(`Column: ${col.name}`, 'column', 'fail', 
        `Column ${col.name} does not exist`);
    }
  }
}

async function verifyIndexes(): Promise<void> {
  console.log('🔍 Verifying database indexes...\n');

  const requiredIndexes = [
    'users_pkey',           // Primary key
    'users_email_key',      // Unique email
    'users_name_unique',    // Unique username (from migration)
    'idx_users_email',      // Email index
    'idx_users_name',       // Username index (from migration)
    'idx_users_role'        // Role index (from migration)
  ];

  for (const indexName of requiredIndexes) {
    const exists = await checkIndexExists(indexName);
    if (exists) {
      addCheck(`Index: ${indexName}`, 'index', 'pass', `Index ${indexName} exists`);
    } else {
      addCheck(`Index: ${indexName}`, 'index', 'fail', `Index ${indexName} missing`);
    }
  }
}

async function verifyConstraints(): Promise<void> {
  console.log('🔍 Verifying database constraints...\n');

  const requiredConstraints = [
    { name: 'users_pkey', type: 'PRIMARY KEY' },
    { name: 'users_email_key', type: 'UNIQUE' },
    { name: 'users_name_unique', type: 'UNIQUE' },
    { name: 'users_role_check', type: 'CHECK' },
    { name: 'users_name_length_check', type: 'CHECK' },
    { name: 'users_email_format_check', type: 'CHECK' }
  ];

  for (const constraint of requiredConstraints) {
    const exists = await checkConstraintExists('users', constraint.name);
    if (exists && exists.constraint_type === constraint.type) {
      addCheck(`Constraint: ${constraint.name}`, 'constraint', 'pass', 
        `Constraint ${constraint.name} exists (${constraint.type})`);
    } else if (exists) {
      addCheck(`Constraint: ${constraint.name}`, 'constraint', 'warning', 
        `Constraint ${constraint.name} exists but wrong type`, 
        '', constraint.type, exists.constraint_type);
    } else {
      addCheck(`Constraint: ${constraint.name}`, 'constraint', 'fail', 
        `Constraint ${constraint.name} missing`);
    }
  }
}

async function verifyTriggers(): Promise<void> {
  console.log('🔍 Verifying database triggers...\n');

  const requiredTriggers = [
    'update_users_updated_at',
    'ensure_admin_exists_trigger',
    'normalize_username_trigger'
  ];

  for (const triggerName of requiredTriggers) {
    const exists = await checkTriggerExists(triggerName, 'users');
    if (exists) {
      addCheck(`Trigger: ${triggerName}`, 'trigger', 'pass', `Trigger ${triggerName} exists`);
    } else {
      addCheck(`Trigger: ${triggerName}`, 'trigger', 'fail', `Trigger ${triggerName} missing`);
    }
  }
}

async function verifyFunctions(): Promise<void> {
  console.log('🔍 Verifying database functions...\n');

  const requiredFunctions = [
    'update_updated_at_column',
    'validate_username',
    'ensure_admin_exists',
    'normalize_username'
  ];

  for (const functionName of requiredFunctions) {
    const exists = await checkFunctionExists(functionName);
    if (exists) {
      addCheck(`Function: ${functionName}`, 'function', 'pass', `Function ${functionName} exists`);
    } else {
      addCheck(`Function: ${functionName}`, 'function', 'fail', `Function ${functionName} missing`);
    }
  }
}

async function verifyDataIntegrity(): Promise<void> {
  console.log('🔍 Verifying data integrity...\n');

  try {
    // Check if at least one admin user exists
    const adminCount = await db.query('SELECT COUNT(*) as count FROM users WHERE role = $1', ['admin']);
    const adminExists = parseInt(adminCount.rows[0].count) > 0;
    
    if (adminExists) {
      addCheck('Admin User', 'table', 'pass', `Admin user exists (${adminCount.rows[0].count} admin(s))`);
    } else {
      addCheck('Admin User', 'table', 'fail', 'No admin user found');
    }

    // Check for duplicate usernames
    const duplicateUsernames = await db.query(`
      SELECT name, COUNT(*) as count 
      FROM users 
      GROUP BY name 
      HAVING COUNT(*) > 1
    `);
    
    if (duplicateUsernames.rows.length === 0) {
      addCheck('Username Uniqueness', 'table', 'pass', 'No duplicate usernames found');
    } else {
      addCheck('Username Uniqueness', 'table', 'fail', 
        `Duplicate usernames found: ${duplicateUsernames.rows.map(r => r.name).join(', ')}`);
    }

    // Check for duplicate emails
    const duplicateEmails = await db.query(`
      SELECT email, COUNT(*) as count 
      FROM users 
      GROUP BY email 
      HAVING COUNT(*) > 1
    `);
    
    if (duplicateEmails.rows.length === 0) {
      addCheck('Email Uniqueness', 'table', 'pass', 'No duplicate emails found');
    } else {
      addCheck('Email Uniqueness', 'table', 'fail', 
        `Duplicate emails found: ${duplicateEmails.rows.map(r => r.email).join(', ')}`);
    }

  } catch (error) {
    addCheck('Data Integrity', 'table', 'fail', `Error checking data integrity: ${error}`);
  }
}

function printResults(): void {
  console.log('\n📊 Database Schema Verification Results:\n');

  const passed = checks.filter(c => c.status === 'pass').length;
  const failed = checks.filter(c => c.status === 'fail').length;
  const warnings = checks.filter(c => c.status === 'warning').length;

  // Group by type
  const groupedChecks = checks.reduce((acc, check) => {
    if (!acc[check.type]) acc[check.type] = [];
    acc[check.type].push(check);
    return acc;
  }, {} as Record<string, SchemaCheck[]>);

  Object.entries(groupedChecks).forEach(([type, typeChecks]) => {
    console.log(`\n${type.toUpperCase()}:`);
    typeChecks.forEach(check => {
      const icon = check.status === 'pass' ? '✅' : check.status === 'fail' ? '❌' : '⚠️';
      console.log(`  ${icon} ${check.name}: ${check.message}`);
      
      if (check.expected && check.actual) {
        console.log(`      Expected: ${JSON.stringify(check.expected)}`);
        console.log(`      Actual: ${JSON.stringify(check.actual)}`);
      }
    });
  });

  console.log(`\n📈 Summary: ${passed} passed, ${failed} failed, ${warnings} warnings\n`);

  if (failed === 0) {
    console.log('🎉 Database schema verification completed successfully!');
    console.log('✅ All required tables, indexes, constraints, and triggers are properly configured.');
  } else {
    console.log('❌ Database schema has issues that need to be resolved.');
    console.log('\n🔧 Recommended actions:');
    console.log('   1. Run database migrations: npm run db:migrate');
    console.log('   2. Check migration files in src/database/migrations/');
    console.log('   3. Verify database connection and permissions');
    process.exit(1);
  }
}

async function main(): Promise<void> {
  console.log('🔍 Database Schema Verification for JWT Authentication\n');

  try {
    // Check database connection
    const isHealthy = await db.healthCheck();
    if (!isHealthy) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful\n');

    // Run all verification checks
    await verifyUsersTable();
    await verifyIndexes();
    await verifyConstraints();
    await verifyTriggers();
    await verifyFunctions();
    await verifyDataIntegrity();

    // Print results
    printResults();

  } catch (error) {
    console.error('❌ Database schema verification failed:', error);
    process.exit(1);
  } finally {
    await db.disconnect();
  }
}

if (require.main === module) {
  main();
}

export { main as verifyDatabaseSchema };
