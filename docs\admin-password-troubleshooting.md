# Admin Password Troubleshooting Guide

## Issue: Admin Password "admin123" Not Working

### Root Cause
The initial database migration contained an incorrect password hash that didn't correspond to "admin123". This has been identified and fixed.

### Quick Fix Solutions

#### Option 1: Run the Fix Script (Recommended)
```bash
# This script will automatically fix the admin password
npx tsx scripts/fix-admin-password.ts
```

#### Option 2: Manual Database Update
If you have direct database access:

```sql
-- Update admin password to correct hash for "admin123"
UPDATE users 
SET password_hash = '$2a$10$7R4DKfgMVJNQL/e.N6EtXux7voYmuQDzQBVyWZ2GzqTSjwXFA.fkO',
    name = 'admin',
    email = '<EMAIL>',
    updated_at = CURRENT_TIMESTAMP
WHERE role = 'admin';
```

#### Option 3: Re-run Migrations
```bash
# Drop and recreate the database (WARNING: This will delete all data)
npm run db:reset
npm run db:migrate
npm run db:seed
```

### Verification Steps

#### 1. Check Current Admin User
```bash
npx tsx scripts/check-admin-password.ts
```

#### 2. Test Login via API
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

#### 3. Test Login via Frontend
1. Start the frontend: `npm run dev:frontend`
2. Navigate to login page
3. Use credentials: `admin` / `admin123`

### Correct Admin Credentials

After applying the fix, these credentials should work:

| Field | Value |
|-------|-------|
| **Username** | `admin` |
| **Password** | `admin123` |
| **Email** | `<EMAIL>` |
| **Role** | `admin` |

### Technical Details

#### Password Hash Information
- **Algorithm**: bcrypt
- **Salt Rounds**: 10
- **Correct Hash**: `$2a$10$7R4DKfgMVJNQL/e.N6EtXux7voYmuQDzQBVyWZ2GzqTSjwXFA.fkO`
- **Password**: `admin123`

#### Database Schema
```sql
-- Admin user should look like this:
SELECT id, email, name, role, password_hash 
FROM users 
WHERE role = 'admin';

-- Expected result:
-- id | email              | name  | role  | password_hash
-- 1  | <EMAIL> | admin | admin | $2a$10$7R4DKfgMVJNQL/e.N6EtXux7voYmuQDzQBVyWZ2GzqTSjwXFA.fkO
```

### Common Issues and Solutions

#### Issue: Multiple Admin Users
If you have multiple admin users, identify the correct one:

```sql
-- List all admin users
SELECT id, email, name, role, created_at 
FROM users 
WHERE role = 'admin' 
ORDER BY id;

-- Delete duplicate admin users (keep the first one)
DELETE FROM users 
WHERE role = 'admin' 
AND id > (SELECT MIN(id) FROM users WHERE role = 'admin');
```

#### Issue: Wrong Username Format
The system expects username `admin`, not `Admin User` or other variations:

```sql
-- Fix username format
UPDATE users 
SET name = 'admin' 
WHERE role = 'admin';
```

#### Issue: Wrong Email Domain
Update to the correct email domain:

```sql
-- Fix email domain
UPDATE users 
SET email = '<EMAIL>' 
WHERE role = 'admin';
```

### Prevention for Future

#### 1. Always Use Seed Script
Instead of manual user creation, use the seed script:
```bash
npm run db:seed
```

#### 2. Verify Password Hashes
When creating users programmatically, always verify the hash:
```typescript
import bcrypt from 'bcryptjs';

const password = 'admin123';
const hash = await bcrypt.hash(password, 10);
const isValid = await bcrypt.compare(password, hash);
console.log('Hash valid:', isValid); // Should be true
```

#### 3. Use Environment Variables
For production, consider using environment variables for admin credentials:
```env
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
ADMIN_EMAIL=<EMAIL>
```

### Testing After Fix

#### 1. Backend API Test
```bash
# Should return 200 with token and user data
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

#### 2. Frontend Login Test
1. Open browser to `http://localhost:5173/login`
2. Enter username: `admin`
3. Enter password: `admin123`
4. Click "Sign in"
5. Should redirect to dashboard

#### 3. Admin Functions Test
After login, verify admin functions work:
- Navigate to "User Management" in sidebar
- Try creating a new user
- Verify admin-only features are accessible

### Support

If the issue persists after trying these solutions:

1. **Check Database Connection**: Ensure the database is running and accessible
2. **Check Migration Status**: Verify all migrations have been applied
3. **Check Logs**: Look for authentication errors in server logs
4. **Verify Environment**: Ensure JWT_SECRET is set in .env file

### Contact Information

For additional support, check:
- Server logs: `npm run dev:backend` (check console output)
- Database logs: Check PostgreSQL logs
- Frontend console: Open browser developer tools

---

## Summary

The admin password issue was caused by an incorrect bcrypt hash in the initial migration. The fix involves updating the password hash to the correct value for "admin123". Use the provided scripts or manual SQL commands to resolve the issue.
