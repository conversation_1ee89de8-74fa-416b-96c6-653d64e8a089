import request from 'supertest';
import express from 'express';
import { UserModel } from '../database/models/User';
import authRoutes from '../routes/authRoutes';
import passwordRoutes from '../routes/passwordRoutes';
import { generateToken } from '../utils/tokenUtils';
import { User } from '../types';

// Create test app
const app = express();
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use('/api/auth', passwordRoutes);
app.use('/api/admin', passwordRoutes);

// Mock the UserModel
jest.mock('../database/models/User');
const mockUserModel = UserModel as jest.Mocked<typeof UserModel>;

// Mock logger to avoid console output during tests
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('Password Management API', () => {
  const adminUser: User = {
    id: 1,
    name: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const regularUser: User = {
    id: 2,
    name: 'testuser',
    email: '<EMAIL>',
    role: 'user',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('PUT /api/auth/change-password', () => {
    it('should change password successfully with valid current password', async () => {
      const userToken = generateToken(regularUser);
      const updatedUser = { ...regularUser, updatedAt: new Date() };
      mockUserModel.changePassword.mockResolvedValue(updatedUser);

      const response = await request(app)
        .put('/api/auth/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          currentPassword: 'oldpassword123',
          newPassword: 'newpassword123'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Password changed successfully');
      expect(response.body.user).toEqual({
        id: updatedUser.id,
        username: updatedUser.name,
        role: updatedUser.role,
        email: updatedUser.email,
        createdAt: updatedUser.createdAt.toISOString(),
        updatedAt: updatedUser.updatedAt.toISOString()
      });
      expect(mockUserModel.changePassword).toHaveBeenCalledWith(
        regularUser.id,
        'oldpassword123',
        'newpassword123'
      );
    });

    it('should return 400 for missing current password', async () => {
      const userToken = generateToken(regularUser);

      const response = await request(app)
        .put('/api/auth/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          newPassword: 'newpassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Current password and new password are required',
        code: 'MISSING_REQUIRED_FIELDS',
        statusCode: 400
      });
    });

    it('should return 400 for missing new password', async () => {
      const userToken = generateToken(regularUser);

      const response = await request(app)
        .put('/api/auth/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          currentPassword: 'oldpassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Current password and new password are required',
        code: 'MISSING_REQUIRED_FIELDS',
        statusCode: 400
      });
    });

    it('should return 400 for new password too short', async () => {
      const userToken = generateToken(regularUser);

      const response = await request(app)
        .put('/api/auth/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          currentPassword: 'oldpassword123',
          newPassword: '123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'New password must be at least 6 characters long',
        code: 'INVALID_PASSWORD',
        statusCode: 400
      });
    });

    it('should return 400 for incorrect current password', async () => {
      const userToken = generateToken(regularUser);
      mockUserModel.changePassword.mockRejectedValue(new Error('Current password is incorrect'));

      const response = await request(app)
        .put('/api/auth/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'newpassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Current password is incorrect',
        code: 'INVALID_CURRENT_PASSWORD',
        statusCode: 400
      });
    });

    it('should return 400 when new password is same as current', async () => {
      const userToken = generateToken(regularUser);
      mockUserModel.changePassword.mockRejectedValue(new Error('New password must be different from current password'));

      const response = await request(app)
        .put('/api/auth/change-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          currentPassword: 'samepassword123',
          newPassword: 'samepassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'New password must be different from current password',
        code: 'SAME_PASSWORD',
        statusCode: 400
      });
    });

    it('should return 401 for missing authentication', async () => {
      const response = await request(app)
        .put('/api/auth/change-password')
        .send({
          currentPassword: 'oldpassword123',
          newPassword: 'newpassword123'
        });

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe('NO_TOKEN');
    });
  });

  describe('PUT /api/admin/reset-password', () => {
    it('should reset user password successfully as admin', async () => {
      const adminToken = generateToken(adminUser);
      const updatedUser = { ...regularUser, updatedAt: new Date() };
      mockUserModel.updatePasswordByUsername.mockResolvedValue(updatedUser);

      const response = await request(app)
        .put('/api/admin/reset-password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'testuser',
          newPassword: 'resetpassword123'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Password reset successfully');
      expect(response.body.user).toEqual({
        id: updatedUser.id,
        username: updatedUser.name,
        role: updatedUser.role,
        email: updatedUser.email,
        createdAt: updatedUser.createdAt.toISOString(),
        updatedAt: updatedUser.updatedAt.toISOString()
      });
      expect(mockUserModel.updatePasswordByUsername).toHaveBeenCalledWith(
        'testuser',
        'resetpassword123'
      );
    });

    it('should return 403 for non-admin user', async () => {
      const userToken = generateToken(regularUser);

      const response = await request(app)
        .put('/api/admin/reset-password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          username: 'testuser',
          newPassword: 'resetpassword123'
        });

      expect(response.status).toBe(403);
      expect(response.body.error).toEqual({
        message: 'Access denied. Admin privileges required.',
        code: 'INSUFFICIENT_PERMISSIONS',
        statusCode: 403
      });
    });

    it('should return 400 for missing username', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .put('/api/admin/reset-password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          newPassword: 'resetpassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username and new password are required',
        code: 'MISSING_REQUIRED_FIELDS',
        statusCode: 400
      });
    });

    it('should return 400 for missing new password', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .put('/api/admin/reset-password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'testuser'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username and new password are required',
        code: 'MISSING_REQUIRED_FIELDS',
        statusCode: 400
      });
    });

    it('should return 400 for invalid password length', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .put('/api/admin/reset-password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'testuser',
          newPassword: '123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'New password must be at least 6 characters long',
        code: 'INVALID_PASSWORD',
        statusCode: 400
      });
    });

    it('should return 400 for invalid username length', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .put('/api/admin/reset-password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'ab',
          newPassword: 'resetpassword123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username must be at least 3 characters long',
        code: 'INVALID_USERNAME',
        statusCode: 400
      });
    });

    it('should return 404 for non-existent user', async () => {
      const adminToken = generateToken(adminUser);
      mockUserModel.updatePasswordByUsername.mockRejectedValue(new Error('User not found'));

      const response = await request(app)
        .put('/api/admin/reset-password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'nonexistent',
          newPassword: 'resetpassword123'
        });

      expect(response.status).toBe(404);
      expect(response.body.error).toEqual({
        message: 'User not found',
        code: 'USER_NOT_FOUND',
        statusCode: 404
      });
    });

    it('should return 401 for missing authentication', async () => {
      const response = await request(app)
        .put('/api/admin/reset-password')
        .send({
          username: 'testuser',
          newPassword: 'resetpassword123'
        });

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe('NO_TOKEN');
    });
  });
});
