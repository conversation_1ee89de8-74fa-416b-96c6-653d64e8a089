import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { authMiddleware } from '../middleware/authMiddleware';
import logger from '../utils/logger';

const router = Router();

/**
 * Authentication Routes
 * Base path: /api/auth
 */

/**
 * POST /api/auth/login
 * Public endpoint for user authentication
 * Accepts: { username: string, password: string }
 * Returns: { token: string, user: object }
 */
router.post('/login', (req, res, next) => {
  logger.info('Login attempt', {
    username: req.body.username,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  AuthController.login(req, res).catch(next);
});

/**
 * GET /api/auth/profile
 * Protected endpoint to get authenticated user's profile
 * Requires: Valid JWT token in Authorization header
 * Returns: { user: object }
 */
router.get('/profile', authMiddleware, (req, res, next) => {
  logger.info('Profile request', {
    userId: (req as any).user?.id,
    ip: req.ip
  });
  
  AuthController.profile(req, res).catch(next);
});

export default router;
