# AI Telecalling SaaS Platform

A comprehensive AI-powered telecalling platform built for the Indian SMB market, featuring real-time voice processing, lead management, and multi-provider telephony integration.

## 🚀 Features

- **AI-Powered Voice Calls**: Real-time speech-to-text, AI response generation, and text-to-speech
- **Multi-Provider Telephony**: Exotel integration with <PERSON>wilio fallback support
- **Lead Management**: Complete CRUD operations for lead tracking and management
- **Bulk DND Validation**: CSV upload for Do Not Disturb registry compliance checking
- **Real-time Webhooks**: Exotel webhook integration for call status and voice processing
- **Database Integration**: PostgreSQL with Railway cloud hosting
- **Voice Processing Pipeline**: LiteLLM integration for AI conversations
- **Call Session Management**: In-memory and persistent call state tracking

## 🛠 Tech Stack

- **Backend**: Node.js, TypeScript, Express.js
- **Database**: PostgreSQL (Railway)
- **AI Services**: LiteLLM, OpenAI-compatible APIs
- **Telephony**: Exotel (Primary), <PERSON><PERSON><PERSON> (Fallback)
- **Testing**: Jest with integration tests
- **Deployment**: Railway

## 📋 Prerequisites

- Node.js 20+
- PostgreSQL database
- Exotel account with API credentials
- LiteLLM API access

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/sanjeev23oct/tele-ai.git
   cd tele-ai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env` file in the root directory:
   ```env
   # Database Configuration
   DATABASE_URL=postgresql://username:password@host:port/database
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=tele_ai
   DB_USER=your_username
   DB_PASSWORD=your_password

   # Exotel Configuration
   EXOTEL_ACCOUNT_SID=your_account_sid
   EXOTEL_API_KEY=your_api_key
   EXOTEL_API_TOKEN=your_api_token
   EXOTEL_PHONE_NUMBER=your_exotel_number

   # LiteLLM Configuration
   LITELLM_BASE_URL=your_litellm_url
   LITELLM_API_KEY=your_api_key

   # DND API Configuration
   DND_API_BASE_URL=https://api.dndservice.com
   DND_API_TOKEN=your_dnd_api_token
   DND_API_TIMEOUT=30000
   MAX_CONCURRENT_REQUESTS=10
   RETRY_ATTEMPTS=2
   RETRY_DELAY_MS=1000
   MAX_FILE_SIZE_MB=50
   MAX_RECORDS_LIMIT=100000

   # JWT Configuration
   JWT_SECRET=your_jwt_secret
   JWT_EXPIRES_IN=24h

   # Server Configuration
   PORT=3000
   NODE_ENV=development
   ```

4. **Database Setup**
   ```bash
   npm run db:migrate
   ```

5. **Create Initial Users**
   ```bash
   npx tsx scripts/create-initial-users.ts
   ```

## 🚀 Usage

### Development
```bash
# Start backend server
npm run dev:backend

# Run tests
npm test

# Run integration tests
npm run test:integration

# Run demo
npm run demo

# Test authentication system
npm test src/tests/auth.test.ts src/tests/user.test.ts
```

### Authentication
The system includes JWT-based authentication with role-based access control:

**Default Users:**
- Admin: `username=admin, password=admin123`
- Demo: `username=demo, password=demo123`
- Test: `username=testuser, password=test123`

**Example Login:**
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

For detailed authentication documentation, see [docs/authentication-system.md](docs/authentication-system.md)

### Production
```bash
# Build the project
npm run build

# Start production server
npm start
```

## 📡 API Endpoints

### Health Check
- `GET /health` - Server health status

### Authentication
- `POST /api/auth/login` - User login with username/password
- `GET /api/auth/profile` - Get authenticated user profile (requires JWT)

### User Management (Admin Only)
- `POST /api/users` - Create new user (admin only)
- `GET /api/users` - List all users (admin only)

### Leads Management
- `GET /api/v1/leads` - Get all leads
- `POST /api/v1/leads` - Create new lead
- `GET /api/v1/leads/:id` - Get lead by ID
- `PUT /api/v1/leads/:id` - Update lead
- `DELETE /api/v1/leads/:id` - Delete lead

### Calls Management
- `POST /api/v1/calls/initiate` - Initiate AI call
- `GET /api/v1/calls/:callSid` - Get call session
- `POST /api/v1/calls/:callSid/end` - End call
- `GET /api/v1/calls/active` - Get active calls

### DND Validation
- `POST /api/v1/dnd/upload` - Upload CSV for bulk DND validation
- `GET /api/v1/dnd/health` - Check DND service health
- `GET /api/v1/dnd/config` - Get service configuration
- `POST /api/v1/dnd/validate-single` - Validate single phone number

### Webhooks (Exotel Integration)
- `POST /webhook/exotel/voice` - Voice call webhook
- `POST /webhook/exotel/recording` - Audio recording webhook
- `POST /webhook/exotel/status` - Call status webhook

## 📋 DND Validation API

### Bulk CSV Upload

Upload a CSV file for bulk DND validation:

```bash
curl -X POST http://localhost:3000/api/v1/dnd/upload \
  -F "csvFile=@leads.csv" \
  -H "Content-Type: multipart/form-data"
```

**CSV Format Requirements:**
- Required column: `phone`
- Optional columns: `name`, `email`
- Maximum file size: 50MB
- Maximum records: 100,000
- Supported formats: CSV only

**Response:**
```json
{
  "success": true,
  "summary": {
    "totalRecords": 1000,
    "processedRecords": 950,
    "skippedRecords": 50,
    "dndCount": 120,
    "nonDndCount": 830,
    "errorCount": 50
  },
  "results": [
    {
      "name": "John Doe",
      "phone": "+**********",
      "email": "<EMAIL>",
      "dnd_status": "DND"
    }
  ],
  "errors": [
    {
      "row": 5,
      "reason": "phone: Invalid phone number format"
    }
  ],
  "metadata": {
    "processingTimeMs": 15000,
    "fileName": "leads.csv",
    "fileSize": 1024000,
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

### Single Phone Validation

Validate a single phone number:

```bash
curl -X POST http://localhost:3000/api/v1/dnd/validate-single \
  -H "Content-Type: application/json" \
  -d '{"phone": "+**********"}'
```

### Service Health Check

Check DND service status:

```bash
curl -X GET http://localhost:3000/api/v1/dnd/health
```

### Configuration

Get service configuration and limits:

```bash
curl -X GET http://localhost:3000/api/v1/dnd/config
```

## 🔄 Call Flow

1. **Lead Creation**: Create or import leads via API
2. **Call Initiation**: Start AI call through Exotel
3. **Voice Processing**: Real-time speech-to-text conversion
4. **AI Response**: Generate contextual responses using LiteLLM
5. **Speech Synthesis**: Convert AI responses to speech
6. **Call Management**: Track call status and outcomes
7. **Data Persistence**: Save call records and transcripts

## 🧪 Testing

The platform includes comprehensive test coverage:

```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# Test with real Exotel API (up to KYC validation)
npm run demo
```

## 🚀 Deployment

### Railway Deployment
1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on push to main branch

### Manual Deployment
```bash
npm run build
npm start
```

## 📊 Current Status

- ✅ **Core Backend**: Complete and functional
- ✅ **Database Integration**: Working with Railway PostgreSQL
- ✅ **Exotel Integration**: Tested up to KYC compliance
- ✅ **Webhook System**: Fully implemented
- ✅ **AI Processing**: LiteLLM integration complete
- ✅ **Call Management**: Session tracking and persistence
- ✅ **DND Validation**: Bulk CSV upload with comprehensive validation
- 🔄 **Frontend**: Pending development
- 🔄 **User Authentication**: Basic implementation needed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions, please open an issue in the GitHub repository.
