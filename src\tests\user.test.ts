import request from 'supertest';
import express from 'express';
import { UserModel } from '../database/models/User';
import userRoutes from '../routes/userRoutes';
import { generateToken } from '../utils/tokenUtils';
import { User } from '../types';

// Create test app
const app = express();
app.use(express.json());
app.use('/api/users', userRoutes);

// Mock the UserModel
jest.mock('../database/models/User');
const mockUserModel = UserModel as jest.Mocked<typeof UserModel>;

// Mock logger to avoid console output during tests
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}));

describe('User Management API', () => {
  const adminUser: User = {
    id: 1,
    name: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const regularUser: User = {
    id: 2,
    name: 'user',
    email: '<EMAIL>',
    role: 'user',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/users', () => {
    const newUser: User = {
      id: 3,
      name: 'newuser',
      email: '<EMAIL>',
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('should create user successfully with admin token', async () => {
      const adminToken = generateToken(adminUser);
      mockUserModel.createByUsername.mockResolvedValue(newUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newuser',
          password: 'password123',
          role: 'user'
        });

      expect(response.status).toBe(201);
      expect(response.body.user).toEqual({
        id: newUser.id,
        username: newUser.name,
        role: newUser.role,
        email: newUser.email,
        createdAt: newUser.createdAt.toISOString(),
        updatedAt: newUser.updatedAt.toISOString()
      });
      expect(mockUserModel.createByUsername).toHaveBeenCalledWith({
        username: 'newuser',
        password: 'password123',
        role: 'user'
      });
    });

    it('should create admin user when role is specified', async () => {
      const adminToken = generateToken(adminUser);
      const newAdminUser = { ...newUser, role: 'admin' as const };
      mockUserModel.createByUsername.mockResolvedValue(newAdminUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newadmin',
          password: 'password123',
          role: 'admin'
        });

      expect(response.status).toBe(201);
      expect(response.body.user.role).toBe('admin');
      expect(mockUserModel.createByUsername).toHaveBeenCalledWith({
        username: 'newadmin',
        password: 'password123',
        role: 'admin'
      });
    });

    it('should default to user role when role not specified', async () => {
      const adminToken = generateToken(adminUser);
      mockUserModel.createByUsername.mockResolvedValue(newUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newuser',
          password: 'password123'
        });

      expect(response.status).toBe(201);
      expect(mockUserModel.createByUsername).toHaveBeenCalledWith({
        username: 'newuser',
        password: 'password123',
        role: 'user'
      });
    });

    it('should return 403 for non-admin user', async () => {
      const userToken = generateToken(regularUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          username: 'newuser',
          password: 'password123'
        });

      expect(response.status).toBe(403);
      expect(response.body.error).toEqual({
        message: 'Access denied. Admin privileges required.',
        code: 'INSUFFICIENT_PERMISSIONS',
        statusCode: 403
      });
    });

    it('should return 401 for missing token', async () => {
      const response = await request(app)
        .post('/api/users')
        .send({
          username: 'newuser',
          password: 'password123'
        });

      expect(response.status).toBe(401);
      expect(response.body.error).toEqual({
        message: 'Access denied. No token provided.',
        code: 'NO_TOKEN',
        statusCode: 401
      });
    });

    it('should return 400 for missing username', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          password: 'password123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username and password are required',
        code: 'MISSING_REQUIRED_FIELDS',
        statusCode: 400
      });
    });

    it('should return 400 for missing password', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newuser'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username and password are required',
        code: 'MISSING_REQUIRED_FIELDS',
        statusCode: 400
      });
    });

    it('should return 400 for invalid username (too short)', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'ab',
          password: 'password123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Username must be at least 3 characters long',
        code: 'INVALID_USERNAME',
        statusCode: 400
      });
    });

    it('should return 400 for invalid password (too short)', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newuser',
          password: '123'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Password must be at least 6 characters long',
        code: 'INVALID_PASSWORD',
        statusCode: 400
      });
    });

    it('should return 400 for invalid role', async () => {
      const adminToken = generateToken(adminUser);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newuser',
          password: 'password123',
          role: 'invalidrole'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toEqual({
        message: 'Role must be either "admin" or "user"',
        code: 'INVALID_ROLE',
        statusCode: 400
      });
    });

    it('should return 409 for duplicate username', async () => {
      const adminToken = generateToken(adminUser);
      const error = new Error('User with this username already exists');
      mockUserModel.createByUsername.mockRejectedValue(error);

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'existinguser',
          password: 'password123'
        });

      expect(response.status).toBe(409);
      expect(response.body.error).toEqual({
        message: 'Username already exists',
        code: 'USERNAME_EXISTS',
        statusCode: 409
      });
    });

    it('should handle database errors gracefully', async () => {
      const adminToken = generateToken(adminUser);
      mockUserModel.createByUsername.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          username: 'newuser',
          password: 'password123'
        });

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        message: 'Internal server error during user creation',
        code: 'USER_CREATION_ERROR',
        statusCode: 500
      });
    });
  });

  describe('GET /api/users', () => {
    const allUsers = [adminUser, regularUser];

    it('should return all users for admin', async () => {
      const adminToken = generateToken(adminUser);
      mockUserModel.findAll.mockResolvedValue(allUsers);

      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.users).toHaveLength(2);
      expect(response.body.count).toBe(2);
      expect(response.body.users[0]).toEqual({
        id: adminUser.id,
        username: adminUser.name,
        role: adminUser.role,
        email: adminUser.email,
        createdAt: adminUser.createdAt.toISOString(),
        updatedAt: adminUser.updatedAt.toISOString()
      });
    });

    it('should return 403 for non-admin user', async () => {
      const userToken = generateToken(regularUser);

      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.error).toEqual({
        message: 'Access denied. Admin privileges required.',
        code: 'INSUFFICIENT_PERMISSIONS',
        statusCode: 403
      });
    });

    it('should return 401 for missing token', async () => {
      const response = await request(app)
        .get('/api/users');

      expect(response.status).toBe(401);
      expect(response.body.error).toEqual({
        message: 'Access denied. No token provided.',
        code: 'NO_TOKEN',
        statusCode: 401
      });
    });
  });
});
