import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './authMiddleware';
import logger from '../utils/logger';

/**
 * Admin authorization middleware
 * Verifies that the authenticated user has admin role
 * Must be used after authMiddleware to ensure req.user is populated
 * 
 * @param req - Express request object with user data
 * @param res - Express response object
 * @param next - Express next function
 */
export const isAdminMiddleware = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    // Check if user is authenticated (should be set by authMiddleware)
    if (!req.user) {
      logger.error('Admin check failed: No user data found in request', {
        path: req.path,
        ip: req.ip
      });
      
      res.status(401).json({
        error: {
          message: 'Authentication required.',
          code: 'AUTHENTICATION_REQUIRED',
          statusCode: 401
        }
      });
      return;
    }

    // Check if user has admin role
    if (req.user.role !== 'admin') {
      logger.warn('Admin access denied: Insufficient permissions', {
        userId: req.user.id,
        username: req.user.username,
        role: req.user.role,
        path: req.path,
        ip: req.ip
      });
      
      res.status(403).json({
        error: {
          message: 'Access denied. Admin privileges required.',
          code: 'INSUFFICIENT_PERMISSIONS',
          statusCode: 403
        }
      });
      return;
    }

    logger.info('Admin access granted', {
      userId: req.user.id,
      username: req.user.username,
      path: req.path
    });

    next();
  } catch (error) {
    logger.error('Admin middleware error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      path: req.path,
      ip: req.ip
    });

    res.status(500).json({
      error: {
        message: 'Internal server error during authorization.',
        code: 'AUTHORIZATION_ERROR',
        statusCode: 500
      }
    });
  }
};
