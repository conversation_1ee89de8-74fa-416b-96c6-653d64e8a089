# Authentication System Documentation

## Overview

The Tele-AI platform implements a comprehensive JWT-based authentication system with role-based access control (RBAC). The system follows a modular architecture pattern with clear separation of concerns.

## Architecture

### Core Components

```
src/
├── controllers/
│   ├── authController.ts    # Login and profile endpoints
│   └── userController.ts    # User management (admin-only)
├── middleware/
│   ├── authMiddleware.ts    # JWT verification
│   └── isAdminMiddleware.ts # Admin role validation
├── models/
│   └── userModel.ts         # User data operations
├── routes/
│   ├── authRoutes.ts        # Authentication endpoints
│   └── userRoutes.ts        # User management endpoints
├── utils/
│   └── tokenUtils.ts        # JWT helper functions
└── tests/
    ├── auth.test.ts         # Authentication tests
    ├── user.test.ts         # User management tests
    └── auth-integration.test.ts # End-to-end tests
```

## Security Features

### Password Security
- **Hashing**: bcryptjs with exactly 10 salt rounds
- **Minimum length**: 6 characters
- **No password exposure**: Passwords never returned in API responses

### JWT Implementation
- **Algorithm**: HS256 (HMAC with SHA-256)
- **Expiration**: Exactly 24 hours (86400 seconds)
- **Claims**: User ID, username, role, issued at, expiration
- **Secret**: Stored securely in environment variables

### Role-Based Access Control
- **Roles**: `admin`, `user`
- **Admin privileges**: User creation, user listing
- **User privileges**: Profile access only
- **Middleware enforcement**: Automatic role validation

## API Endpoints

### Authentication Endpoints

#### POST /api/auth/login
**Description**: Authenticate user with username/password
**Access**: Public
**Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```
**Success Response** (200):
```json
{
  "token": "jwt_token_string",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### GET /api/auth/profile
**Description**: Get authenticated user's profile
**Access**: Requires valid JWT token
**Headers**: `Authorization: Bearer <jwt_token>`
**Success Response** (200):
```json
{
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### User Management Endpoints

#### POST /api/users
**Description**: Create new user (admin only)
**Access**: Requires admin JWT token
**Headers**: `Authorization: Bearer <admin_jwt_token>`
**Request Body**:
```json
{
  "username": "string",
  "password": "string",
  "role": "admin" | "user" // optional, defaults to "user"
}
```

#### GET /api/users
**Description**: List all users (admin only)
**Access**: Requires admin JWT token
**Headers**: `Authorization: Bearer <admin_jwt_token>`
**Success Response** (200):
```json
{
  "users": [...],
  "count": 5
}
```

### Password Management Endpoints

#### PUT /api/auth/change-password
**Description**: Change user's own password
**Access**: Requires valid JWT token + current password verification
**Headers**: `Authorization: Bearer <jwt_token>`
**Request Body**:
```json
{
  "currentPassword": "string",
  "newPassword": "string"
}
```
**Success Response** (200):
```json
{
  "message": "Password changed successfully",
  "user": {
    "id": 1,
    "username": "user",
    "role": "user",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### PUT /api/admin/reset-password
**Description**: Reset any user's password (admin only)
**Access**: Requires admin JWT token
**Headers**: `Authorization: Bearer <admin_jwt_token>`
**Request Body**:
```json
{
  "username": "string",
  "newPassword": "string"
}
```
**Success Response** (200):
```json
{
  "message": "Password reset successfully",
  "user": {
    "id": 2,
    "username": "targetuser",
    "role": "user",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## Error Handling

### Standard Error Response Format
```json
{
  "error": {
    "message": "Human readable error message",
    "code": "ERROR_CODE",
    "statusCode": 400
  }
}
```

### Common Error Codes
- `NO_TOKEN`: Missing Authorization header
- `INVALID_TOKEN`: Malformed or invalid JWT
- `TOKEN_EXPIRED`: JWT token has expired
- `INVALID_CREDENTIALS`: Wrong username/password
- `INSUFFICIENT_PERMISSIONS`: Non-admin accessing admin endpoint
- `MISSING_CREDENTIALS`: Required fields missing
- `USERNAME_EXISTS`: Username already taken
- `INVALID_CURRENT_PASSWORD`: Current password is incorrect
- `SAME_PASSWORD`: New password is same as current password
- `INVALID_PASSWORD`: Password doesn't meet requirements
- `USER_NOT_FOUND`: Target user doesn't exist

## Usage Examples

### Login Flow
```bash
# 1. Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Response includes token
# {"token":"eyJhbGciOiJIUzI1NiIs...","user":{...}}

# 2. Use token for authenticated requests
curl -X GET http://localhost:3000/api/auth/profile \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIs..."
```

### Admin Operations
```bash
# Create new user (admin only)
curl -X POST http://localhost:3000/api/users \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{"username":"newuser","password":"password123","role":"user"}'

# List all users (admin only)
curl -X GET http://localhost:3000/api/users \
  -H "Authorization: Bearer <admin_token>"

# Change own password
curl -X PUT http://localhost:3000/api/auth/change-password \
  -H "Authorization: Bearer <user_token>" \
  -H "Content-Type: application/json" \
  -d '{"currentPassword":"oldpass123","newPassword":"newpass123"}'

# Admin reset user password
curl -X PUT http://localhost:3000/api/admin/reset-password \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{"username":"targetuser","newPassword":"resetpass123"}'
```

## Testing

### Running Tests
```bash
# Run all authentication tests
npm test src/tests/auth.test.ts src/tests/user.test.ts src/tests/auth-integration.test.ts

# Run specific test suite
npm test src/tests/auth.test.ts
```

### Test Coverage
- ✅ Login scenarios (valid/invalid credentials)
- ✅ JWT token validation (valid/expired/malformed)
- ✅ Role-based access control
- ✅ Input validation and sanitization
- ✅ Error handling and edge cases
- ✅ Security (no sensitive data exposure)

## Setup and Configuration

### Environment Variables
```env
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=24h
```

### Initial Users Setup
```bash
# Run the setup script to create initial users
npx tsx scripts/create-initial-users.ts
```

### Default Credentials
- **Admin**: username=`admin`, password=`admin123`
- **Demo**: username=`demo`, password=`demo123`
- **Test**: username=`testuser`, password=`test123`

## Security Best Practices

1. **JWT Secret**: Use a strong, randomly generated secret
2. **HTTPS**: Always use HTTPS in production
3. **Token Storage**: Store tokens securely on client side
4. **Token Rotation**: Implement refresh token mechanism for production
5. **Rate Limiting**: Add rate limiting to login endpoints
6. **Input Validation**: Validate and sanitize all inputs
7. **Logging**: Log authentication events for security monitoring

## Future Enhancements

- [ ] Refresh token mechanism
- [ ] Password reset functionality
- [ ] Multi-factor authentication (MFA)
- [ ] Account lockout after failed attempts
- [ ] Password complexity requirements
- [ ] Session management and revocation
- [ ] OAuth2/SSO integration
