import axios from 'axios';
import { User, ApiError } from '../types';

// Get API base URL from environment or use relative path for production
const getApiBaseUrl = () => {
  // In development, use localhost
  if (import.meta.env.DEV) {
    return 'http://localhost:3000/api';
  }
  // In production, use relative path (same domain)
  return '/api';
};

// Create axios instance for auth API
const authApi = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include JWT token
authApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('teleai_jwt_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
authApi.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle token expiration
    if (error.response?.status === 401) {
      const errorCode = error.response?.data?.error?.code;
      if (errorCode === 'TOKEN_EXPIRED' || errorCode === 'INVALID_TOKEN') {
        // Clear invalid token and redirect to login
        localStorage.removeItem('teleai_jwt_token');
        localStorage.removeItem('teleai_user');
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Authentication API interfaces
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  role?: 'admin' | 'user';
}

export interface CreateUserResponse {
  user: User;
}

export interface GetUsersResponse {
  users: User[];
  count: number;
}

// Authentication service
export const authService = {
  /**
   * Login with username and password
   */
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    try {
      const response = await authApi.post<LoginResponse>('/auth/login', credentials);
      
      // Store JWT token
      localStorage.setItem('teleai_jwt_token', response.data.token);
      
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.data) {
        const apiError = error.response.data as ApiError;
        throw new Error(apiError.error.message || 'Login failed');
      }
      throw new Error('Network error during login');
    }
  },

  /**
   * Get current user profile
   */
  getProfile: async (): Promise<User> => {
    try {
      const response = await authApi.get<{ user: User }>('/auth/profile');
      return response.data.user;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.data) {
        const apiError = error.response.data as ApiError;
        throw new Error(apiError.error.message || 'Failed to get profile');
      }
      throw new Error('Network error during profile fetch');
    }
  },

  /**
   * Create new user (admin only)
   */
  createUser: async (userData: CreateUserRequest): Promise<User> => {
    try {
      const response = await authApi.post<CreateUserResponse>('/users', userData);
      return response.data.user;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.data) {
        const apiError = error.response.data as ApiError;
        throw new Error(apiError.error.message || 'Failed to create user');
      }
      throw new Error('Network error during user creation');
    }
  },

  /**
   * Get all users (admin only)
   */
  getUsers: async (): Promise<GetUsersResponse> => {
    try {
      const response = await authApi.get<GetUsersResponse>('/users');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.data) {
        const apiError = error.response.data as ApiError;
        throw new Error(apiError.error.message || 'Failed to get users');
      }
      throw new Error('Network error during users fetch');
    }
  },

  /**
   * Logout user
   */
  logout: (): void => {
    localStorage.removeItem('teleai_jwt_token');
    localStorage.removeItem('teleai_user');
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('teleai_jwt_token');
    if (!token) return false;

    try {
      // Decode JWT to check expiration (basic check)
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      if (payload.exp && payload.exp < currentTime) {
        // Token expired, clean up
        authService.logout();
        return false;
      }
      
      return true;
    } catch {
      // Invalid token format, clean up
      authService.logout();
      return false;
    }
  },

  /**
   * Get stored JWT token
   */
  getToken: (): string | null => {
    return localStorage.getItem('teleai_jwt_token');
  },

  /**
   * Get user data from token (without API call)
   */
  getUserFromToken: (): User | null => {
    const token = localStorage.getItem('teleai_jwt_token');
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      
      // Check if token is expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < currentTime) {
        return null;
      }

      // Return user data from token payload
      return {
        id: payload.id,
        name: payload.username,
        email: '', // Will be filled by profile API call
        role: payload.role,
        createdAt: '',
        updatedAt: ''
      };
    } catch {
      return null;
    }
  },

  /**
   * Verify token and get fresh user data
   */
  verifyAndRefreshUser: async (): Promise<User | null> => {
    if (!authService.isAuthenticated()) {
      return null;
    }

    try {
      const user = await authService.getProfile();
      // Store user data for offline access
      localStorage.setItem('teleai_user', JSON.stringify(user));
      return user;
    } catch {
      // If profile fetch fails, clear auth data
      authService.logout();
      return null;
    }
  }
};

export default authService;
