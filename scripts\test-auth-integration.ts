#!/usr/bin/env tsx

/**
 * Integration test script for the authentication system
 * Tests the complete flow: login -> profile -> user creation -> logout
 */

import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

interface LoginResponse {
  token: string;
  user: {
    id: number;
    username: string;
    role: string;
    email: string;
    createdAt: string;
    updatedAt: string;
  };
}

interface User {
  id: number;
  username: string;
  role: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

async function testAuthenticationFlow() {
  console.log('🧪 Testing Authentication System Integration\n');

  try {
    // Test 1: Login with admin credentials
    console.log('1️⃣ Testing admin login...');
    const loginResponse = await axios.post<LoginResponse>(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });

    if (loginResponse.status !== 200) {
      throw new Error(`Login failed with status: ${loginResponse.status}`);
    }

    const { token, user } = loginResponse.data;
    console.log(`✅ Login successful! User: ${user.username}, Role: ${user.role}`);
    console.log(`🔑 Token received (length: ${token.length})`);

    // Test 2: Get profile with JWT token
    console.log('\n2️⃣ Testing profile endpoint...');
    const profileResponse = await axios.get<{ user: User }>(`${API_BASE_URL}/auth/profile`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    if (profileResponse.status !== 200) {
      throw new Error(`Profile request failed with status: ${profileResponse.status}`);
    }

    console.log(`✅ Profile retrieved! User: ${profileResponse.data.user.username}`);

    // Test 3: Create a new user (admin only)
    console.log('\n3️⃣ Testing user creation (admin only)...');
    const newUsername = `testuser_${Date.now()}`;
    const createUserResponse = await axios.post<{ user: User }>(`${API_BASE_URL}/users`, {
      username: newUsername,
      password: 'testpass123',
      role: 'user'
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    if (createUserResponse.status !== 201) {
      throw new Error(`User creation failed with status: ${createUserResponse.status}`);
    }

    console.log(`✅ User created! Username: ${createUserResponse.data.user.username}`);

    // Test 4: Get all users (admin only)
    console.log('\n4️⃣ Testing get all users (admin only)...');
    const getUsersResponse = await axios.get<{ users: User[]; count: number }>(`${API_BASE_URL}/users`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    if (getUsersResponse.status !== 200) {
      throw new Error(`Get users failed with status: ${getUsersResponse.status}`);
    }

    console.log(`✅ Users retrieved! Total count: ${getUsersResponse.data.count}`);

    // Test 5: Login with the newly created user
    console.log('\n5️⃣ Testing login with newly created user...');
    const newUserLoginResponse = await axios.post<LoginResponse>(`${API_BASE_URL}/auth/login`, {
      username: newUsername,
      password: 'testpass123'
    });

    if (newUserLoginResponse.status !== 200) {
      throw new Error(`New user login failed with status: ${newUserLoginResponse.status}`);
    }

    const newUserToken = newUserLoginResponse.data.token;
    console.log(`✅ New user login successful! Role: ${newUserLoginResponse.data.user.role}`);

    // Test 6: Try to create user with non-admin account (should fail)
    console.log('\n6️⃣ Testing user creation with non-admin account (should fail)...');
    try {
      await axios.post(`${API_BASE_URL}/users`, {
        username: 'shouldfail',
        password: 'password123',
        role: 'user'
      }, {
        headers: {
          Authorization: `Bearer ${newUserToken}`
        }
      });
      throw new Error('User creation should have failed for non-admin user');
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 403) {
        console.log('✅ Non-admin user correctly denied access to user creation');
      } else {
        throw error;
      }
    }

    // Test 7: Test invalid token
    console.log('\n7️⃣ Testing invalid token (should fail)...');
    try {
      await axios.get(`${API_BASE_URL}/auth/profile`, {
        headers: {
          Authorization: 'Bearer invalid.jwt.token'
        }
      });
      throw new Error('Profile request should have failed with invalid token');
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        console.log('✅ Invalid token correctly rejected');
      } else {
        throw error;
      }
    }

    // Test 8: Test missing token
    console.log('\n8️⃣ Testing missing token (should fail)...');
    try {
      await axios.get(`${API_BASE_URL}/auth/profile`);
      throw new Error('Profile request should have failed without token');
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        console.log('✅ Missing token correctly rejected');
      } else {
        throw error;
      }
    }

    console.log('\n🎉 All authentication tests passed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Admin login');
    console.log('   ✅ Profile retrieval with JWT');
    console.log('   ✅ User creation (admin only)');
    console.log('   ✅ Get all users (admin only)');
    console.log('   ✅ New user login');
    console.log('   ✅ Non-admin access control');
    console.log('   ✅ Invalid token handling');
    console.log('   ✅ Missing token handling');

  } catch (error) {
    console.error('\n❌ Authentication test failed:');
    if (axios.isAxiosError(error)) {
      console.error(`   Status: ${error.response?.status}`);
      console.error(`   Message: ${error.response?.data?.error?.message || error.message}`);
      console.error(`   Code: ${error.response?.data?.error?.code || 'N/A'}`);
    } else {
      console.error(`   Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    process.exit(1);
  }
}

async function checkServerHealth() {
  try {
    console.log('🔍 Checking server health...');
    const response = await axios.get('http://localhost:3000/health');
    if (response.status === 200) {
      console.log('✅ Server is healthy and running');
      return true;
    }
  } catch (error) {
    console.error('❌ Server health check failed. Make sure the server is running on port 3000.');
    console.error('   Run: npm run dev:backend');
    return false;
  }
  return false;
}

// Main execution
async function main() {
  console.log('🚀 Authentication System Integration Test\n');
  
  const isServerHealthy = await checkServerHealth();
  if (!isServerHealthy) {
    process.exit(1);
  }

  await testAuthenticationFlow();
}

if (require.main === module) {
  main();
}

export { testAuthenticationFlow };
