import { Request, Response, NextFunction } from 'express';
import { verifyToken, extractTokenFromHeader, JWTPayload } from '../utils/tokenUtils';
import logger from '../utils/logger';

/**
 * Extended Request interface to include user data
 */
export interface AuthenticatedRequest extends Request {
  user?: JWTPayload;
}

/**
 * Authentication middleware to verify JWT tokens
 * Extracts token from Authorization header, verifies it, and populates req.user
 * 
 * @param req - Express request object
 * @param res - Express response object  
 * @param next - Express next function
 */
export const authMiddleware = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      logger.warn('Authentication failed: No token provided', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path
      });
      
      res.status(401).json({
        error: {
          message: 'Access denied. No token provided.',
          code: 'NO_TOKEN',
          statusCode: 401
        }
      });
      return;
    }

    // Verify the token
    const decoded = verifyToken(token);
    
    // Populate req.user with decoded payload
    req.user = decoded;
    
    logger.info('Authentication successful', {
      userId: decoded.id,
      username: decoded.username,
      role: decoded.role,
      path: req.path
    });

    next();
  } catch (error) {
    logger.warn('Authentication failed: Invalid token', {
      error: error instanceof Error ? error.message : 'Unknown error',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });

    // Determine specific error type
    if (error instanceof Error) {
      if (error.message === 'Token expired') {
        res.status(401).json({
          error: {
            message: 'Access denied. Token expired.',
            code: 'TOKEN_EXPIRED',
            statusCode: 401
          }
        });
        return;
      } else if (error.message === 'Invalid token') {
        res.status(401).json({
          error: {
            message: 'Access denied. Invalid token.',
            code: 'INVALID_TOKEN',
            statusCode: 401
          }
        });
        return;
      }
    }

    // Generic token error
    res.status(401).json({
      error: {
        message: 'Access denied. Token verification failed.',
        code: 'TOKEN_VERIFICATION_FAILED',
        statusCode: 401
      }
    });
  }
};
