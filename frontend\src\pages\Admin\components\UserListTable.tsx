import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Users, Search, Filter, ChevronUp, ChevronDown, AlertCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '../../../store/authStore';
import { User } from '../../../types';
import authService from '../../../services/authService';

interface UserListTableProps {
  refreshTrigger: number;
}

export const UserListTable: React.FC<UserListTableProps> = ({ refreshTrigger }) => {
  const { error, isLoading } = useAuth();

  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'user'>('all');
  const [sortField, setSortField] = useState<'name' | 'role' | 'email' | 'createdAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use ref to track the last refresh trigger to prevent unnecessary fetches
  const lastRefreshTriggerRef = useRef<number>(-1);
  const mountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);



  // Fetch users function - stable reference
  const fetchUsers = useCallback(async () => {
    if (!mountedRef.current) return;

    setIsRefreshing(true);

    try {
      const response = await authService.getUsers();

      // if (mountedRef.current) {
        const usersArray = response.users || [];
        setUsers(usersArray);
      // }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      if (mountedRef.current) {
        setUsers([]);
      }
    } finally {
      if (mountedRef.current) {
        setIsRefreshing(false);
      }
    }
  }, []); // Empty dependency array for stable reference

  // Effect for initial load and refresh trigger
  useEffect(() => {
    // Only fetch if this is the initial load or refreshTrigger has changed
    if (lastRefreshTriggerRef.current !== refreshTrigger && mountedRef.current) {
      lastRefreshTriggerRef.current = refreshTrigger;
      fetchUsers();
    }
  }, [refreshTrigger]); // Only depend on refreshTrigger

  // Filter and sort users
  useEffect(() => {
    // Safety check for users array
    if (!Array.isArray(users)) {
      setFilteredUsers([]);
      return;
    }

    if (users.length === 0) {
      setFilteredUsers([]);
      return;
    }

    let filtered = [...users];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user?.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    // Apply sorting
    const safeSortOrder = sortOrder || 'desc';

    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case 'name':
          aValue = (a?.name || '').toLowerCase();
          bValue = (b?.name || '').toLowerCase();
          break;
        case 'role':
          aValue = a?.role || '';
          bValue = b?.role || '';
          break;
        case 'email':
          aValue = (a?.email || '').toLowerCase();
          bValue = (b?.email || '').toLowerCase();
          break;
        case 'createdAt':
          aValue = a?.createdAt ? new Date(a.createdAt).getTime() : 0;
          bValue = b?.createdAt ? new Date(b.createdAt).getTime() : 0;
          break;
        default:
          aValue = (a?.name || '').toLowerCase();
          bValue = (b?.name || '').toLowerCase();
      }

      if (aValue < bValue) return safeSortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return safeSortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter, sortField, sortOrder]);

  const handleSort = (field: 'name' | 'role' | 'email' | 'createdAt') => {
    if (!mountedRef.current) return;

    if (sortField === field) {
      // Toggle sort order for the same field
      const newSortOrder = (sortOrder === 'asc') ? 'desc' : 'asc';
      setSortOrder(newSortOrder);
    } else {
      // New field, start with ascending
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRoleBadgeColor = (role: string) => {
    return role === 'admin' 
      ? 'bg-purple-100 text-purple-800' 
      : 'bg-blue-100 text-blue-800';
  };

  const SortIcon: React.FC<{ field: 'name' | 'role' | 'email' | 'createdAt' }> = ({ field }) => {
    if (sortField !== field) {
      return <ChevronUp className="h-4 w-4 text-gray-400" />;
    }
    return sortOrder === 'asc' 
      ? <ChevronUp className="h-4 w-4 text-primary-600" />
      : <ChevronDown className="h-4 w-4 text-primary-600" />;
  };

  return (
    <div className="bg-white shadow-soft rounded-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Users className="h-6 w-6 text-primary-600 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              Users ({filteredUsers.length})
            </h2>
          </div>
          <button
            onClick={fetchUsers}
            disabled={isRefreshing || isLoading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          {/* Role Filter */}
          <div className="sm:w-48">
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value as 'all' | 'admin' | 'user')}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Roles</option>
                <option value="admin">Admin</option>
                <option value="user">User</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="px-6 py-4 bg-red-50 border-b border-red-200">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {/* { isRefreshing && (
        <div className="px-6 py-8 text-center">
          <div className="inline-flex items-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mr-3"></div>
            <span className="text-gray-600">Loading users...</span>
          </div>
        </div>
      )} */}



      {/* Table */}
      {/* {!isRefreshing && ( */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    Username
                    <SortIcon field="name" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('role')}
                >
                  <div className="flex items-center">
                    Role
                    <SortIcon field="role" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('email')}
                >
                  <div className="flex items-center">
                    Email
                    <SortIcon field="email" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('createdAt')}
                >
                  <div className="flex items-center">
                    Created
                    <SortIcon field="createdAt" />
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(() => {
                console.log('🔍 Table rendering check:');
                console.log('filteredUsers.length:', filteredUsers.length);
                console.log('filteredUsers:', filteredUsers);
                console.log('First user:', filteredUsers[0]);
                return null;
              })()}
              {filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-6 py-8 text-center text-gray-500">
                    {searchTerm || roleFilter !== 'all'
                      ? 'No users match your filters'
                      : 'No users found'
                    }
                  </td>
                </tr>
              ) : (
                [
                  // Test row to verify table rendering
                  <tr key="test-row" className="bg-yellow-100">
                    <td className="px-6 py-4 text-sm">TEST ROW</td>
                    <td className="px-6 py-4 text-sm">TEST</td>
                    <td className="px-6 py-4 text-sm"><EMAIL></td>
                    <td className="px-6 py-4 text-sm">Now</td>
                  </tr>,
                  ...filteredUsers.map((user, index) => {
                  console.log(`🔍 Rendering user ${index}:`, user);
                  console.log(`🔍 User properties:`, {
                    id: user?.id,
                    name: user?.name,
                    email: user?.email,
                    role: user?.role,
                    createdAt: user?.createdAt
                  });

                  // Safety check for required properties
                  if (!user || !user.id || !user.name) {
                    console.error('❌ Invalid user object:', user);
                    return null;
                  }

                  return (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary-600 to-purple-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {(user?.name || 'U').charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user?.name || 'Unknown'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(user?.role || 'user')}`}>
                        {user?.role || 'user'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user?.email || 'No email'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(user?.createdAt)}
                    </td>
                  </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};
