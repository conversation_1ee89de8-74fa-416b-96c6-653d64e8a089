#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to check what password the current admin hash corresponds to
 * and fix the admin password issue
 */

import bcrypt from 'bcryptjs';
import { db } from '../src/database/connection';

async function checkAdminPassword() {
  console.log('🔍 Checking admin password hash...\n');

  try {
    // Check database connection
    const isHealthy = await db.healthCheck();
    if (!isHealthy) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful\n');

    // Get current admin user
    const adminResult = await db.query(`
      SELECT id, email, name, password_hash, role 
      FROM users 
      WHERE role = 'admin' 
      LIMIT 1
    `);

    if (adminResult.rows.length === 0) {
      console.log('❌ No admin user found in database');
      return;
    }

    const admin = adminResult.rows[0];
    console.log('👤 Current admin user:');
    console.log(`   ID: ${admin.id}`);
    console.log(`   Email: ${admin.email}`);
    console.log(`   Username: ${admin.name}`);
    console.log(`   Role: ${admin.role}`);
    console.log(`   Password Hash: ${admin.password_hash}\n`);

    // Test common passwords against the hash
    const testPasswords = ['admin123', 'admin', 'password', 'test123', 'demo123'];
    
    console.log('🔐 Testing passwords against current hash:');
    for (const testPassword of testPasswords) {
      const isMatch = await bcrypt.compare(testPassword, admin.password_hash);
      console.log(`   ${testPassword}: ${isMatch ? '✅ MATCH' : '❌ No match'}`);
    }

    // Generate correct hash for admin123
    console.log('\n🔧 Generating correct hash for "admin123":');
    const correctHash = await bcrypt.hash('admin123', 10);
    console.log(`   New hash: ${correctHash}`);

    // Verify the new hash works
    const verifyNewHash = await bcrypt.compare('admin123', correctHash);
    console.log(`   Verification: ${verifyNewHash ? '✅ Correct' : '❌ Failed'}\n`);

    // Ask if we should update the password
    console.log('🔄 Updating admin password to "admin123"...');
    
    const updateResult = await db.query(`
      UPDATE users 
      SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
      WHERE role = 'admin' AND id = $2
      RETURNING id, name, email
    `, [correctHash, admin.id]);

    if (updateResult.rows.length > 0) {
      console.log('✅ Admin password updated successfully!');
      console.log(`   Updated user: ${updateResult.rows[0].name} (${updateResult.rows[0].email})`);
      
      // Verify the update worked
      console.log('\n🧪 Testing login with updated password...');
      const updatedAdmin = await db.query(`
        SELECT password_hash FROM users WHERE id = $1
      `, [admin.id]);
      
      const loginTest = await bcrypt.compare('admin123', updatedAdmin.rows[0].password_hash);
      console.log(`   Login test: ${loginTest ? '✅ SUCCESS' : '❌ FAILED'}`);
      
    } else {
      console.log('❌ Failed to update admin password');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await db.disconnect();
  }
}

// Run the check
if (require.main === module) {
  checkAdminPassword();
}

export { checkAdminPassword };
