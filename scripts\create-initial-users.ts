#!/usr/bin/env tsx

/**
 * <PERSON><PERSON>t to create initial users for the authentication system
 * This script demonstrates how to use the UserModel to create admin and regular users
 */

import { UserModel } from '../src/database/models/User';
import { db } from '../src/database/connection';
import { config } from '../src/config';

async function createInitialUsers() {
  try {
    console.log('🚀 Creating initial users for Tele-AI authentication system...\n');

    // Check database connection
    const isHealthy = await db.healthCheck();
    if (!isHealthy) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful');

    // Create admin user
    try {
      const adminUser = await UserModel.createByUsername({
        username: 'admin',
        password: 'admin123',
        role: 'admin'
      });
      console.log('✅ Admin user created:', {
        id: adminUser.id,
        username: adminUser.name,
        role: adminUser.role,
        email: adminUser.email
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        console.log('ℹ️  Admin user already exists, skipping...');
      } else {
        throw error;
      }
    }

    // Create demo regular user
    try {
      const demoUser = await UserModel.createByUsername({
        username: 'demo',
        password: 'demo123',
        role: 'user'
      });
      console.log('✅ Demo user created:', {
        id: demoUser.id,
        username: demoUser.name,
        role: demoUser.role,
        email: demoUser.email
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        console.log('ℹ️  Demo user already exists, skipping...');
      } else {
        throw error;
      }
    }

    // Create test user
    try {
      const testUser = await UserModel.createByUsername({
        username: 'testuser',
        password: 'test123',
        role: 'user'
      });
      console.log('✅ Test user created:', {
        id: testUser.id,
        username: testUser.name,
        role: testUser.role,
        email: testUser.email
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        console.log('ℹ️  Test user already exists, skipping...');
      } else {
        throw error;
      }
    }

    // Display summary
    const userCount = await UserModel.count();
    console.log(`\n📊 Total users in system: ${userCount}`);

    console.log('\n🎉 Initial users setup complete!');
    console.log('\n📝 You can now test the authentication system with these credentials:');
    console.log('   Admin: username=admin, password=admin123');
    console.log('   Demo:  username=demo, password=demo123');
    console.log('   Test:  username=testuser, password=test123');
    
    console.log('\n🔗 API Endpoints:');
    console.log('   POST /api/auth/login - Login with username/password');
    console.log('   GET  /api/auth/profile - Get user profile (requires JWT)');
    console.log('   POST /api/users - Create new user (admin only)');
    console.log('   GET  /api/users - List all users (admin only)');

    console.log('\n💡 Example usage:');
    console.log('   curl -X POST http://localhost:3000/api/auth/login \\');
    console.log('        -H "Content-Type: application/json" \\');
    console.log('        -d \'{"username":"admin","password":"admin123"}\'');

  } catch (error) {
    console.error('❌ Error creating initial users:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await db.disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  createInitialUsers();
}

export { createInitialUsers };
