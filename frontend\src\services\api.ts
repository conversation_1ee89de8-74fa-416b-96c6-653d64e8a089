import axios from 'axios';
import { 
  Lead, 
  Agent, 
  Call, 
  CreateLeadRequest, 
  UpdateLeadRequest,
  CreateAgentRequest,
  UpdateAgentRequest,
  InitiateCallRequest 
} from '../types';

// Get API base URL from environment or use relative path for production
const getApiBaseUrl = () => {
  // In development, use localhost
  if (import.meta.env.DEV) {
    return 'http://localhost:3000/api/v1';
  }
  // In production, use relative path (same domain)
  return '/api/v1';
};

// Create axios instance with base configuration
const api = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('teleai_jwt_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle token expiration
    if (error.response?.status === 401) {
      const errorCode = error.response?.data?.error?.code;
      if (errorCode === 'TOKEN_EXPIRED' || errorCode === 'INVALID_TOKEN') {
        // Clear invalid token and redirect to login
        localStorage.removeItem('teleai_jwt_token');
        localStorage.removeItem('teleai_user');
        window.location.href = '/login';
      }
    }
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Leads API
export const leadsApi = {
  // Get all leads with pagination
  getAll: async (page = 1, limit = 10): Promise<{ leads: Lead[]; pagination: any }> => {
    const response = await api.get(`/leads?page=${page}&limit=${limit}`);
    return response.data;
  },

  // Get lead by ID
  getById: async (id: number): Promise<Lead> => {
    const response = await api.get(`/leads/${id}`);
    return response.data.lead;
  },

  // Create new lead
  create: async (leadData: CreateLeadRequest): Promise<Lead> => {
    const response = await api.post('/leads', leadData);
    return response.data.lead;
  },

  // Update lead
  update: async (id: number, leadData: UpdateLeadRequest): Promise<Lead> => {
    const response = await api.put(`/leads/${id}`, leadData);
    return response.data.lead;
  },

  // Delete lead
  delete: async (id: number): Promise<void> => {
    await api.delete(`/leads/${id}`);
  },

  // Get lead stats
  getStats: async (): Promise<{ total: number; byStatus: Record<string, number> }> => {
    const response = await api.get('/leads/stats/overview');
    return response.data.stats;
  },
};

// Calls API
export const callsApi = {
  // Get all active calls
  getActive: async (): Promise<Call[]> => {
    const response = await api.get('/calls');
    return response.data.calls;
  },

  // Get call by ID
  getById: async (callSid: string): Promise<Call> => {
    const response = await api.get(`/calls/${callSid}`);
    return response.data;
  },

  // Initiate a call
  initiate: async (callData: InitiateCallRequest): Promise<{ callSid: string; status: string; startedAt: string }> => {
    const response = await api.post('/calls/initiate', callData);
    return response.data;
  },

  // End a call
  end: async (callSid: string): Promise<{ callSid: string; status: string; endedAt: string }> => {
    const response = await api.post(`/calls/${callSid}/end`);
    return response.data;
  },
};

// Agents API (Mock implementation - will be replaced with real API when backend is implemented)
export const agentsApi = {
  // Get all agents
  getAll: async (): Promise<Agent[]> => {
    // Mock data for now - replace with real API call when backend is ready
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: 1,
            name: 'John Smith',
            email: '<EMAIL>',
            phoneNumber: '+91-9876543210',
            skills: ['Sales', 'Tech Support', 'Lead Qualification'],
            status: 'active',
            assignedLeads: 25,
            totalCalls: 156,
            successRate: 72.5,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-20T15:30:00Z',
          },
          {
            id: 2,
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            phoneNumber: '+91-9876543211',
            skills: ['Customer Service', 'Product Demo'],
            status: 'busy',
            assignedLeads: 18,
            totalCalls: 89,
            successRate: 68.2,
            createdAt: '2024-01-10T09:00:00Z',
            updatedAt: '2024-01-20T14:15:00Z',
          },
          {
            id: 3,
            name: 'Mike Wilson',
            email: '<EMAIL>',
            phoneNumber: '+91-9876543212',
            skills: ['Lead Qualification', 'Follow-up'],
            status: 'inactive',
            assignedLeads: 0,
            totalCalls: 45,
            successRate: 55.6,
            createdAt: '2024-01-05T11:00:00Z',
            updatedAt: '2024-01-18T16:45:00Z',
          },
        ]);
      }, 500);
    });
  },

  // Get agent by ID
  getById: async (id: number): Promise<Agent | null> => {
    const agents = await agentsApi.getAll();
    return agents.find(agent => agent.id === id) || null;
  },

  // Create new agent
  create: async (agentData: CreateAgentRequest): Promise<Agent> => {
    // Mock implementation - replace with real API call
    return new Promise((resolve) => {
      setTimeout(() => {
        const newAgent: Agent = {
          id: Date.now(), // Mock ID
          ...agentData,
          status: 'active',
          assignedLeads: 0,
          totalCalls: 0,
          successRate: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        resolve(newAgent);
      }, 500);
    });
  },

  // Update agent
  update: async (id: number, agentData: UpdateAgentRequest): Promise<Agent> => {
    // Mock implementation - replace with real API call
    const agent = await agentsApi.getById(id);
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const updatedAgent: Agent = {
          ...agent,
          ...agentData,
          updatedAt: new Date().toISOString(),
        };
        resolve(updatedAgent);
      }, 500);
    });
  },

  // Delete agent
  // @ts-ignore - Parameter will be used when implementing real API
  delete: async (id: number): Promise<void> => {
    // Mock implementation - replace with real API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  },
};

export default api;
