import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './store/authStore';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { Layout } from './components/layout/Layout';
import { LoginPage } from './pages/Auth/LoginPage';
import { Dashboard } from './pages/Dashboard/Dashboard';
import { LeadsPage } from './pages/Leads/LeadsPage';
import { AgentsPage } from './pages/Agents/AgentsPage';
import { CallsPage } from './pages/Calls/CallsPage';
import { ConversationTestPage } from './pages/ConversationTest';
import { DndValidationPage } from './pages/DndValidationPage';
import { UserManagementPage } from './pages/Admin/UserManagementPage';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<LoginPage />} />

            {/* Protected Routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout>
                  <Navigate to="/dashboard" replace />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/leads" element={
              <ProtectedRoute>
                <Layout>
                  <LeadsPage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/agents" element={
              <ProtectedRoute>
                <Layout>
                  <AgentsPage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/calls" element={
              <ProtectedRoute>
                <Layout>
                  <CallsPage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/conversation-test" element={
              <ProtectedRoute>
                <Layout>
                  <ConversationTestPage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/dnd-validation" element={
              <ProtectedRoute>
                <Layout>
                  <DndValidationPage />
                </Layout>
              </ProtectedRoute>
            } />

            {/* Admin Routes */}
            <Route path="/admin/users" element={
              <ProtectedRoute adminOnly={true}>
                <Layout>
                  <UserManagementPage />
                </Layout>
              </ProtectedRoute>
            } />

            {/* Fallback */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
