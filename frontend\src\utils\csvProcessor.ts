import <PERSON> from 'papa<PERSON><PERSON>';
import { DNDRecord } from '../types';

export interface CSVValidationResult {
  isValid: boolean;
  errors: string[];
  preview: any[];
  totalRows: number;
  headers: string[];
}

export interface CSVExportOptions {
  filename?: string;
  includeHeaders?: boolean;
  selectedColumns?: string[];
}

// Smart format detection for CSV data
interface CSVFormatDetection {
  hasHeaders: boolean;
  phoneColumnIndex: number;
  processedData: string[];
}

const detectCSVFormat = (rawData: string[][]): CSVFormatDetection => {
  if (rawData.length === 0) {
    return { hasHeaders: false, phoneColumnIndex: -1, processedData: [] };
  }

  const firstRow = rawData[0];

  // Check if first row looks like headers
  const hasHeaders = firstRow.some(cell =>
    cell && typeof cell === 'string' &&
    /^(phone|mobile|number|contact|tel|telephone)$/i.test(cell.trim())
  );

  let phoneColumnIndex = -1;
  let processedData: string[] = [];

  if (hasHeaders) {
    // Find phone column index
    phoneColumnIndex = firstRow.findIndex(cell =>
      cell && /^(phone|mobile|number|contact|tel|telephone)$/i.test(cell.trim())
    );

    // Extract phone numbers from subsequent rows
    processedData = rawData.slice(1)
      .map(row => row[phoneColumnIndex] || '')
      .filter(phone => phone && phone.trim())
      .map(phone => cleanPhoneNumber(phone));
  } else {
    // No headers - assume first column contains phone numbers
    phoneColumnIndex = 0;
    processedData = rawData
      .map(row => row[0] || '')
      .filter(phone => phone && phone.trim())
      .map(phone => cleanPhoneNumber(phone));
  }

  return { hasHeaders, phoneColumnIndex, processedData };
};

// Clean and normalize phone number
const cleanPhoneNumber = (phone: string): string => {
  if (!phone) return '';

  // Remove all non-digit characters except +
  let cleaned = phone.toString().replace(/[^\d+]/g, '');

  // Remove leading zeros
  cleaned = cleaned.replace(/^0+/, '');

  // Add country code for Indian numbers if missing
  if (cleaned.length === 10 && /^[6-9]/.test(cleaned)) {
    cleaned = '+91' + cleaned;
  }

  return cleaned;
};

// Smart CSV validation that handles various formats
export const validateCSVFile = (file: File): Promise<CSVValidationResult> => {
  return new Promise((resolve) => {
    const errors: string[] = [];

    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      errors.push('File must be a CSV file (.csv extension)');
    }

    // Check file size (50MB limit)
    const maxSizeBytes = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSizeBytes) {
      errors.push(`File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds the 50MB limit`);
    }

    // First, try parsing without headers to detect format
    Papa.parse(file, {
      header: false,
      skipEmptyLines: true,
      preview: 10,
      complete: (results) => {
        const rawData = results.data as string[][];

        if (rawData.length === 0) {
          errors.push('CSV file is empty');
          resolve({
            isValid: false,
            errors,
            preview: [],
            totalRows: 0,
            headers: [],
          });
          return;
        }

        // Smart format detection
        const { hasHeaders, phoneColumnIndex, processedData } = detectCSVFormat(rawData);

        if (phoneColumnIndex === -1) {
          errors.push('No valid phone numbers found. Please ensure your CSV contains phone numbers.');
        }

        // Validate phone numbers in preview
        const validPhones = processedData.filter((phone: string) => isValidPhoneNumber(phone));
        if (validPhones.length === 0) {
          errors.push('No valid phone numbers found. Phone numbers should be 10+ digits.');
        }

        // Check record limit
        const estimatedRows = Math.floor(file.size / (file.size / rawData.length));
        if (estimatedRows > 100000) {
          errors.push(`Estimated ${estimatedRows.toLocaleString()} records exceeds the 100,000 record limit`);
        }

        // Create preview data
        const preview = processedData.slice(0, 5).map((phone: string) => ({ phone }));

        resolve({
          isValid: errors.length === 0,
          errors,
          preview,
          totalRows: estimatedRows,
          headers: hasHeaders ? ['phone'] : [],
        });
      },
      error: (error) => {
        errors.push(`Failed to parse CSV: ${error.message}`);
        resolve({
          isValid: false,
          errors,
          preview: [],
          totalRows: 0,
          headers: [],
        });
      }
    });
  });
};

// Process phone numbers from text input
export const processTextInput = (text: string): CSVValidationResult => {
  const errors: string[] = [];

  if (!text || !text.trim()) {
    errors.push('Please enter some phone numbers');
    return {
      isValid: false,
      errors,
      preview: [],
      totalRows: 0,
      headers: ['phone'],
    };
  }

  // Split by various delimiters (newlines, commas, semicolons, spaces)
  const rawNumbers = text
    .split(/[\n\r,;|\s]+/)
    .map(num => num.trim())
    .filter(num => num.length > 0);

  if (rawNumbers.length === 0) {
    errors.push('No phone numbers found in the text');
    return {
      isValid: false,
      errors,
      preview: [],
      totalRows: 0,
      headers: ['phone'],
    };
  }

  // Clean and validate phone numbers
  const processedNumbers = rawNumbers.map(cleanPhoneNumber).filter(num => num.length > 0);
  const validNumbers = processedNumbers.filter(num => isValidPhoneNumber(num));

  if (validNumbers.length === 0) {
    errors.push('No valid phone numbers found. Phone numbers should be 10+ digits.');
  }

  // Check record limit
  if (processedNumbers.length > 100000) {
    errors.push(`${processedNumbers.length.toLocaleString()} numbers exceeds the 100,000 record limit`);
  }

  // Create preview
  const preview = validNumbers.slice(0, 5).map((phone: string) => ({ phone }));

  return {
    isValid: errors.length === 0,
    errors,
    preview,
    totalRows: processedNumbers.length,
    headers: ['phone'],
  };
};

// Parse CSV file completely (for preview purposes)
export const parseCSVFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          reject(new Error(`CSV parsing failed: ${results.errors.map(e => e.message).join(', ')}`));
        } else {
          resolve(results.data);
        }
      },
      error: (error) => {
        reject(error);
      }
    });
  });
};

// Export data to CSV
export const exportToCSV = (
  data: DNDRecord[], 
  options: CSVExportOptions = {}
): void => {
  const {
    filename = `dnd-results-${new Date().toISOString().split('T')[0]}.csv`,
    includeHeaders = true,
    selectedColumns
  } = options;
  
  let exportData = data;
  
  // Filter columns if specified
  if (selectedColumns && selectedColumns.length > 0) {
    exportData = data.map(row => {
      const filteredRow: any = {};
      selectedColumns.forEach(col => {
        if (col in row) {
          filteredRow[col] = (row as any)[col];
        }
      });
      return filteredRow;
    });
  }
  
  const csv = Papa.unparse(exportData, {
    header: includeHeaders,
  });
  
  // Create and trigger download
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

// Export filtered data based on DND status
export const exportFilteredData = (
  data: DNDRecord[],
  filter: 'all' | 'DND' | 'Non-DND' | 'Error',
  filename?: string
): void => {
  let filteredData = data;
  let filterSuffix = '';
  
  switch (filter) {
    case 'DND':
      filteredData = data.filter(record => record.dnd_status === 'DND');
      filterSuffix = '-dnd-only';
      break;
    case 'Non-DND':
      filteredData = data.filter(record => record.dnd_status === 'Non-DND');
      filterSuffix = '-non-dnd-only';
      break;
    case 'Error':
      filteredData = data.filter(record => record.dnd_status === 'Error');
      filterSuffix = '-errors-only';
      break;
    default:
      filterSuffix = '-all-results';
  }
  
  const defaultFilename = `dnd-results${filterSuffix}-${new Date().toISOString().split('T')[0]}.csv`;
  
  exportToCSV(filteredData, {
    filename: filename || defaultFilename,
  });
};

// Format file size for display
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Validate phone number format (enhanced validation)
export const isValidPhoneNumber = (phone: string): boolean => {
  if (!phone || typeof phone !== 'string') return false;

  // Remove all non-digit characters except + at the beginning
  const cleaned = phone.replace(/[^\d+]/g, '');

  // Must have at least 10 digits
  if (cleaned.length < 10) return false;

  // Check various valid patterns
  const patterns = [
    /^\+\d{10,15}$/, // International format with +
    /^\d{10,15}$/, // Just digits
    /^91\d{10}$/, // India with 91 prefix
    /^[6-9]\d{9}$/, // Indian mobile (10 digits starting with 6-9)
  ];

  return patterns.some(pattern => pattern.test(cleaned));
};

// Validate email format
export const isValidEmail = (email: string): boolean => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

// Generate example phone numbers for demonstration
export const getExamplePhoneNumbers = (): string => {
  return `9811569014
8800794738
+91-9876543210
91 9123456789
(98) 1156-9014`;
};

// Generate example CSV content
export const getExampleCSVContent = (): string => {
  return `name,phone,email
John Doe,9811569014,<EMAIL>
Jane Smith,8800794738,<EMAIL>
Bob Johnson,+91-9876543210,<EMAIL>`;
};
