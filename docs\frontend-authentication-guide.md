# Frontend Authentication System Guide

## Overview

The Tele-AI frontend now includes a comprehensive JWT-based authentication system that replaces the previous demo implementation. This guide covers the complete integration and usage of the authentication system.

## Architecture Overview

### Authentication Flow
```
1. User enters username/password → LoginPage
2. Frontend calls POST /api/auth/login → Backend
3. Backend validates credentials → Returns JWT token + user data
4. Frontend stores JWT token → localStorage
5. All subsequent API calls include JW<PERSON> token → Authorization header
6. Backend validates JWT on each request → Grants/denies access
```

### Key Components

#### 1. Authentication Service (`src/services/authService.ts`)
Centralized API service for all authentication operations:
- `login(credentials)` - User authentication
- `getProfile()` - Get current user data
- `createUser(userData)` - Admin-only user creation
- `getUsers()` - Admin-only user listing
- `logout()` - Clear authentication data
- `isAuthenticated()` - Check token validity
- `verifyAndRefreshUser()` - Validate and refresh user data

#### 2. Authentication Store (`src/store/authStore.tsx`)
React Context-based state management:
- Manages authentication state (user, isAuthenticated, isLoading, error)
- Provides authentication methods to components
- Handles automatic token validation on app initialization
- Manages error states and loading indicators

#### 3. Protected Route Component (`src/components/auth/ProtectedRoute.tsx`)
Route protection with role-based access control:
- Validates JWT tokens before rendering protected content
- Supports admin-only routes with `adminOnly` prop
- Shows loading states during authentication checks
- Displays access denied messages for insufficient permissions

#### 4. Admin User Management Interface
Complete admin interface for user management:
- `UserManagementPage.tsx` - Main admin page
- `UserCreationForm.tsx` - Form for creating new users
- `UserListTable.tsx` - Table for viewing and managing users

## Usage Guide

### 1. Authentication in Components

```typescript
import { useAuth } from '../store/authStore';

function MyComponent() {
  const { user, isAuthenticated, isLoading, error, login, logout } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }

  return (
    <div>
      <p>Welcome, {user?.name}!</p>
      <p>Role: {user?.role}</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### 2. Protected Routes

```typescript
// Regular protected route
<Route path="/dashboard" element={
  <ProtectedRoute>
    <Layout>
      <Dashboard />
    </Layout>
  </ProtectedRoute>
} />

// Admin-only protected route
<Route path="/admin/users" element={
  <ProtectedRoute adminOnly={true}>
    <Layout>
      <UserManagementPage />
    </Layout>
  </ProtectedRoute>
} />
```

### 3. API Calls with Authentication

The authentication system automatically adds JWT tokens to API requests:

```typescript
import api from '../services/api';

// Token is automatically added to Authorization header
const response = await api.get('/leads');
```

### 4. Admin User Management

Admins can access the user management interface at `/admin/users`:
- Create new users with username, password, and role
- View all users in a sortable, filterable table
- Real-time validation and error handling
- Responsive design for mobile and desktop

## Security Features

### 1. JWT Token Management
- Tokens stored securely in localStorage with key `teleai_jwt_token`
- Automatic token expiration handling (24-hour expiry)
- Token validation on app initialization
- Automatic cleanup on logout or token expiration

### 2. Role-Based Access Control
- Admin role: Full access including user management
- User role: Standard access to leads, calls, dashboard
- Route-level protection with `ProtectedRoute` component
- API-level protection with JWT middleware

### 3. Input Validation
- Client-side validation for all forms
- Real-time validation feedback
- Server-side validation integration
- Comprehensive error handling

### 4. Error Handling
- Automatic token expiration detection
- Network error handling
- User-friendly error messages
- Loading states for all async operations

## Default Credentials

The system includes default users for testing:

| Username | Password | Role  | Description |
|----------|----------|-------|-------------|
| `admin`  | `admin123` | admin | Full system access |
| `demo`   | `demo123`  | user  | Standard user access |
| `testuser` | `test123` | user  | Test account |

## API Integration

### Authentication Endpoints

#### POST /api/auth/login
```typescript
// Request
{
  "username": "admin",
  "password": "admin123"
}

// Response
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### GET /api/auth/profile
```typescript
// Headers: Authorization: Bearer <token>
// Response
{
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### User Management Endpoints (Admin Only)

#### POST /api/users
```typescript
// Headers: Authorization: Bearer <admin_token>
// Request
{
  "username": "newuser",
  "password": "password123",
  "role": "user"
}

// Response
{
  "user": {
    "id": 3,
    "username": "newuser",
    "role": "user",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### GET /api/users
```typescript
// Headers: Authorization: Bearer <admin_token>
// Response
{
  "users": [...],
  "count": 5
}
```

## Testing

### Manual Testing
1. Start the backend server: `npm run dev:backend`
2. Start the frontend: `npm run dev:frontend`
3. Navigate to `http://localhost:5173/login`
4. Test login with default credentials
5. Verify admin access to user management
6. Test user creation and role-based access

### Integration Testing
Run the automated integration test:
```bash
npx tsx scripts/test-auth-integration.ts
```

## Troubleshooting

### Common Issues

1. **"Access denied" errors**
   - Check if JWT token is valid and not expired
   - Verify user has appropriate role for the requested resource

2. **Login failures**
   - Verify backend server is running
   - Check username/password combination
   - Check network connectivity

3. **Token expiration**
   - Tokens expire after 24 hours
   - User will be automatically redirected to login
   - No manual intervention required

4. **Admin access issues**
   - Verify user has `admin` role
   - Check if admin routes are properly protected
   - Ensure backend user creation was successful

### Debug Information

Enable verbose logging by setting localStorage:
```javascript
localStorage.setItem('debug', 'auth');
```

Check browser developer tools for:
- Network requests to authentication endpoints
- JWT token in localStorage (`teleai_jwt_token`)
- Console errors or warnings
- Response status codes and error messages

## Migration from Demo System

The new authentication system completely replaces the previous demo implementation:

### Removed
- Hardcoded `DEMO_USERS` array
- Email-based authentication
- localStorage user persistence without JWT

### Added
- JWT-based authentication
- Username-based login
- Role-based access control
- Admin user management interface
- Comprehensive error handling
- Loading states and user feedback

### Breaking Changes
- Login now requires username instead of email
- Authentication state is managed differently
- Protected routes now support role-based access
- API calls now include JWT tokens automatically

The migration is seamless for end users, but developers should update any custom authentication logic to use the new `useAuth` hook and authentication service.
