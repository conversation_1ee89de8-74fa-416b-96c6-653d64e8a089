import { Request, Response } from 'express';
import { UserModel } from '../database/models/User';
import { generateToken } from '../utils/tokenUtils';
import { AuthenticatedRequest } from '../middleware/authMiddleware';
import logger from '../utils/logger';

/**
 * Authentication Controller
 * Handles login and profile endpoints
 */
export class AuthController {
  /**
   * Login endpoint - POST /api/auth/login
   * Accepts {username, password} and returns {token, user} on success
   * 
   * @param req - Express request object
   * @param res - Express response object
   */
  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { username, password } = req.body;

      // Validate required fields
      if (!username || !password) {
        logger.warn('Login attempt with missing credentials', {
          hasUsername: !!username,
          hasPassword: !!password,
          ip: req.ip
        });

        res.status(400).json({
          error: {
            message: 'Username and password are required',
            code: 'MISSING_CREDENTIALS',
            statusCode: 400
          }
        });
        return;
      }

      // Verify user credentials
      const user = await UserModel.verifyPasswordByUsername(username, password);
      
      if (!user) {
        logger.warn('Login failed: Invalid credentials', {
          username,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });

        res.status(401).json({
          error: {
            message: 'Invalid username or password',
            code: 'INVALID_CREDENTIALS',
            statusCode: 401
          }
        });
        return;
      }

      // Generate JWT token
      const token = generateToken(user);

      logger.info('Login successful', {
        userId: user.id,
        username: user.name,
        role: user.role,
        ip: req.ip
      });

      // Return token and user data (without sensitive information)
      res.status(200).json({
        token,
        user: {
          id: user.id,
          name: user.name,
          role: user.role,
          email: user.email,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      });
    } catch (error) {
      logger.error('Login error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        ip: req.ip
      });

      res.status(500).json({
        error: {
          message: 'Internal server error during login',
          code: 'LOGIN_ERROR',
          statusCode: 500
        }
      });
    }
  }

  /**
   * Profile endpoint - GET /api/auth/profile
   * Returns authenticated user's profile data
   * Requires JWT authentication
   * 
   * @param req - Express request object with user data
   * @param res - Express response object
   */
  static async profile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // User data should be populated by authMiddleware
      if (!req.user) {
        logger.error('Profile request without user data', {
          path: req.path,
          ip: req.ip
        });

        res.status(401).json({
          error: {
            message: 'Authentication required',
            code: 'AUTHENTICATION_REQUIRED',
            statusCode: 401
          }
        });
        return;
      }

      // Fetch fresh user data from database
      const user = await UserModel.findById(req.user.id);
      
      if (!user) {
        logger.warn('Profile request for non-existent user', {
          userId: req.user.id,
          ip: req.ip
        });

        res.status(404).json({
          error: {
            message: 'User not found',
            code: 'USER_NOT_FOUND',
            statusCode: 404
          }
        });
        return;
      }

      logger.info('Profile data retrieved', {
        userId: user.id,
        username: user.name
      });

      // Return user profile data (without sensitive information)
      res.status(200).json({
        user: {
          id: user.id,
          name: user.name,
          role: user.role,
          email: user.email,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      });
    } catch (error) {
      logger.error('Profile retrieval error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        userId: req.user?.id,
        ip: req.ip
      });

      res.status(500).json({
        error: {
          message: 'Internal server error during profile retrieval',
          code: 'PROFILE_ERROR',
          statusCode: 500
        }
      });
    }
  }
}
