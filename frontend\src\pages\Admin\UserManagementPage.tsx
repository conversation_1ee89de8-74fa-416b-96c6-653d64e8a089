import React, { useState, useCallback } from 'react';
import { Settings, Users, UserPlus } from 'lucide-react';
import { UserCreationForm } from './components/UserCreationForm';
import { UserListTable } from './components/UserListTable';
import { ErrorBoundary } from '../../components/common/ErrorBoundary';

export const UserManagementPage: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleUserCreated = useCallback(() => {
    // Trigger refresh of user list
    setRefreshTrigger(prev => prev + 1);
  }, []);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow-soft rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-primary-600 to-purple-600 rounded-lg shadow-soft">
              <Settings className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
              <p className="text-gray-600">Create and manage user accounts</p>
            </div>
          </div>
          
          {/* Quick Stats */}
          <div className="hidden sm:flex items-center space-x-6">
            <div className="text-center">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <p className="mt-2 text-sm font-medium text-gray-900">Total Users</p>
              <p className="text-xs text-gray-500">Manage all accounts</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
                <UserPlus className="h-5 w-5 text-green-600" />
              </div>
              <p className="mt-2 text-sm font-medium text-gray-900">Create User</p>
              <p className="text-xs text-gray-500">Add new accounts</p>
            </div>
          </div>
        </div>
      </div>

      {/* Admin Notice */}
      <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">
              Admin Access Required
            </h3>
            <div className="mt-2 text-sm text-amber-700">
              <p>
                This page is restricted to administrators only. You can create new user accounts and manage existing users.
                Please ensure you follow your organization's security policies when creating accounts.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Creation Form - Left Column */}
        <div className="lg:col-span-1">
          <UserCreationForm onUserCreated={handleUserCreated} />
        </div>

        {/* User List Table - Right Column */}
        <div className="lg:col-span-2">
          <ErrorBoundary>
            <UserListTable refreshTrigger={refreshTrigger} />
          </ErrorBoundary>
        </div>
      </div>

      {/* Help Section */}
      <div className="bg-white shadow-soft rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">User Management Guidelines</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Creating Users</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Username must be at least 3 characters long</li>
              <li>• Password must be at least 6 characters long</li>
              <li>• Choose appropriate role (Admin or User)</li>
              <li>• Usernames must be unique across the system</li>
            </ul>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">User Roles</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <span className="font-medium">Admin:</span> Full system access, can manage users</li>
              <li>• <span className="font-medium">User:</span> Standard access to leads, calls, and dashboard</li>
              <li>• Role changes require admin privileges</li>
              <li>• At least one admin account must exist</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Security Best Practices
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                • Use strong, unique passwords for all accounts
                • Regularly review user access and remove unused accounts
                • Grant admin privileges only when necessary
                • Monitor user activity for suspicious behavior
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
