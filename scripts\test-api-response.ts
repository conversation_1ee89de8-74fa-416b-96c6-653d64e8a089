#!/usr/bin/env tsx

/**
 * Test script to verify API response format
 */

import axios from 'axios';

async function testApiResponse() {
  try {
    console.log('🔍 Testing API response format...\n');

    // First login to get a token
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });

    if (loginResponse.status !== 200) {
      throw new Error('Login failed');
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Test the users endpoint
    const usersResponse = await axios.get('http://localhost:3000/api/users', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('📊 Users API Response:');
    console.log('Status:', usersResponse.status);
    console.log('Data:', JSON.stringify(usersResponse.data, null, 2));

    // Verify response structure
    const data = usersResponse.data;
    
    if (!data.users || !Array.isArray(data.users)) {
      console.error('❌ Response does not contain users array');
      return;
    }

    if (typeof data.count !== 'number') {
      console.error('❌ Response does not contain count field');
      return;
    }

    console.log('\n✅ Response structure is correct:');
    console.log(`   - users: array with ${data.users.length} items`);
    console.log(`   - count: ${data.count}`);

    // Check first user structure
    if (data.users.length > 0) {
      const firstUser = data.users[0];
      console.log('\n👤 First user structure:');
      console.log('   - id:', firstUser.id);
      console.log('   - name:', firstUser.name);
      console.log('   - email:', firstUser.email);
      console.log('   - role:', firstUser.role);
      console.log('   - createdAt:', firstUser.createdAt);
      console.log('   - updatedAt:', firstUser.updatedAt);

      // Verify required fields
      const requiredFields = ['id', 'name', 'email', 'role'];
      const missingFields = requiredFields.filter(field => !(field in firstUser));
      
      if (missingFields.length > 0) {
        console.error('❌ Missing required fields:', missingFields);
      } else {
        console.log('✅ All required fields present');
      }
    }

  } catch (error) {
    console.error('❌ API test failed:', error);
    if (axios.isAxiosError(error)) {
      console.error('Response data:', error.response?.data);
      console.error('Response status:', error.response?.status);
    }
  }
}

testApiResponse();
