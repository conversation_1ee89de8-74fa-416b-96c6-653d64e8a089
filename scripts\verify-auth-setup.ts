#!/usr/bin/env tsx

/**
 * Verification script to check if the authentication system is properly set up
 * This script checks file structure, dependencies, and configuration
 */

import fs from 'fs';
import path from 'path';

interface CheckResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
}

const results: CheckResult[] = [];

function addResult(name: string, status: 'pass' | 'fail' | 'warning', message: string) {
  results.push({ name, status, message });
}

function checkFileExists(filePath: string, description: string): boolean {
  const fullPath = path.resolve(filePath);
  if (fs.existsSync(fullPath)) {
    addResult(`File: ${description}`, 'pass', `✅ ${filePath} exists`);
    return true;
  } else {
    addResult(`File: ${description}`, 'fail', `❌ ${filePath} missing`);
    return false;
  }
}

function checkDirectoryExists(dirPath: string, description: string): boolean {
  const fullPath = path.resolve(dirPath);
  if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
    addResult(`Directory: ${description}`, 'pass', `✅ ${dirPath} exists`);
    return true;
  } else {
    addResult(`Directory: ${description}`, 'fail', `❌ ${dirPath} missing`);
    return false;
  }
}

function checkPackageJson() {
  const packageJsonPath = path.resolve('package.json');
  if (!fs.existsSync(packageJsonPath)) {
    addResult('Dependencies', 'fail', '❌ package.json not found');
    return;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const requiredDeps = ['bcryptjs', 'jsonwebtoken', 'dotenv'];
  const requiredDevDeps = ['jest', 'supertest', '@types/bcryptjs', '@types/jsonwebtoken'];

  let allDepsPresent = true;

  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      addResult(`Dependency: ${dep}`, 'pass', `✅ ${dep} installed`);
    } else {
      addResult(`Dependency: ${dep}`, 'fail', `❌ ${dep} missing`);
      allDepsPresent = false;
    }
  });

  requiredDevDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      addResult(`Dev Dependency: ${dep}`, 'pass', `✅ ${dep} installed`);
    } else {
      addResult(`Dev Dependency: ${dep}`, 'fail', `❌ ${dep} missing`);
      allDepsPresent = false;
    }
  });

  if (allDepsPresent) {
    addResult('Dependencies', 'pass', '✅ All required dependencies present');
  }
}

function checkEnvFile() {
  const envPath = path.resolve('.env');
  if (!fs.existsSync(envPath)) {
    addResult('Environment', 'fail', '❌ .env file missing');
    return;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  
  if (envContent.includes('JWT_SECRET=')) {
    addResult('JWT Secret', 'pass', '✅ JWT_SECRET configured');
  } else {
    addResult('JWT Secret', 'fail', '❌ JWT_SECRET missing in .env');
  }

  if (envContent.includes('JWT_EXPIRES_IN=')) {
    addResult('JWT Expiration', 'pass', '✅ JWT_EXPIRES_IN configured');
  } else {
    addResult('JWT Expiration', 'warning', '⚠️ JWT_EXPIRES_IN not set (will use default)');
  }
}

function checkBackendFiles() {
  console.log('🔍 Checking backend authentication files...\n');

  // Core authentication files
  checkFileExists('src/utils/tokenUtils.ts', 'JWT Token Utilities');
  checkFileExists('src/middleware/authMiddleware.ts', 'Authentication Middleware');
  checkFileExists('src/middleware/isAdminMiddleware.ts', 'Admin Authorization Middleware');
  checkFileExists('src/controllers/authController.ts', 'Authentication Controller');
  checkFileExists('src/controllers/userController.ts', 'User Management Controller');
  checkFileExists('src/routes/authRoutes.ts', 'Authentication Routes');
  checkFileExists('src/routes/userRoutes.ts', 'User Management Routes');

  // Test files
  checkFileExists('src/tests/auth.test.ts', 'Authentication Tests');
  checkFileExists('src/tests/user.test.ts', 'User Management Tests');
  checkFileExists('src/tests/auth-integration.test.ts', 'Integration Tests');

  // User model (should already exist)
  checkFileExists('src/database/models/User.ts', 'User Model');
}

function checkFrontendFiles() {
  console.log('🔍 Checking frontend authentication files...\n');

  // Core frontend files
  checkFileExists('frontend/src/services/authService.ts', 'Frontend Auth Service');
  checkFileExists('frontend/src/store/authStore.tsx', 'Authentication Store');
  checkFileExists('frontend/src/components/auth/ProtectedRoute.tsx', 'Protected Route Component');

  // Admin interface
  checkDirectoryExists('frontend/src/pages/Admin', 'Admin Pages Directory');
  checkFileExists('frontend/src/pages/Admin/UserManagementPage.tsx', 'User Management Page');
  checkFileExists('frontend/src/pages/Admin/components/UserCreationForm.tsx', 'User Creation Form');
  checkFileExists('frontend/src/pages/Admin/components/UserListTable.tsx', 'User List Table');

  // Updated files
  checkFileExists('frontend/src/App.tsx', 'Main App Component');
  checkFileExists('frontend/src/types/index.ts', 'Type Definitions');
  checkFileExists('frontend/src/pages/Auth/LoginPage.tsx', 'Login Page');
}

function checkScripts() {
  console.log('🔍 Checking utility scripts...\n');

  checkFileExists('scripts/create-initial-users.ts', 'Initial Users Script');
  checkFileExists('scripts/test-auth-integration.ts', 'Integration Test Script');
  checkFileExists('docs/authentication-system.md', 'Backend Auth Documentation');
  checkFileExists('docs/frontend-authentication-guide.md', 'Frontend Auth Documentation');
}

function printResults() {
  console.log('\n📊 Verification Results:\n');

  const passed = results.filter(r => r.status === 'pass').length;
  const failed = results.filter(r => r.status === 'fail').length;
  const warnings = results.filter(r => r.status === 'warning').length;

  results.forEach(result => {
    const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
    console.log(`${icon} ${result.name}: ${result.message}`);
  });

  console.log(`\n📈 Summary: ${passed} passed, ${failed} failed, ${warnings} warnings\n`);

  if (failed === 0) {
    console.log('🎉 Authentication system setup verification completed successfully!');
    console.log('\n🚀 Next steps:');
    console.log('   1. Start the backend: npm run dev:backend');
    console.log('   2. Create initial users: npx tsx scripts/create-initial-users.ts');
    console.log('   3. Start the frontend: npm run dev:frontend');
    console.log('   4. Test the system: npx tsx scripts/test-auth-integration.ts');
  } else {
    console.log('❌ Authentication system setup has issues that need to be resolved.');
    console.log('\n🔧 Please fix the failed checks above before proceeding.');
    process.exit(1);
  }
}

async function main() {
  console.log('🔍 Authentication System Setup Verification\n');
  console.log('This script checks if all authentication components are properly set up.\n');

  // Check dependencies and configuration
  console.log('🔍 Checking dependencies and configuration...\n');
  checkPackageJson();
  checkEnvFile();

  // Check backend files
  checkBackendFiles();

  // Check frontend files
  checkFrontendFiles();

  // Check utility scripts and documentation
  checkScripts();

  // Print results
  printResults();
}

if (require.main === module) {
  main();
}

export { main as verifyAuthSetup };
