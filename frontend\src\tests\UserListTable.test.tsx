import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { UserListTable } from '../pages/Admin/components/UserListTable';
import { AuthProvider } from '../store/authStore';
import * as authService from '../services/authService';

// Mock the auth service
jest.mock('../services/authService');
const mockAuthService = authService as jest.Mocked<typeof authService>;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

const mockUser = {
  id: 1,
  name: 'admin',
  email: '<EMAIL>',
  role: 'admin' as const,
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
};

const mockUsers = [
  mockUser,
  {
    id: 2,
    name: 'testuser',
    email: '<EMAIL>',
    role: 'user' as const,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }
];

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <AuthProvider>
    {children}
  </AuthProvider>
);

describe('UserListTable Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock localStorage
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'teleai_jwt_token') return 'mock-jwt-token';
      if (key === 'teleai_user') return JSON.stringify(mockUser);
      return null;
    });

    // Mock auth service methods
    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getProfile.mockResolvedValue(mockUser);
    mockAuthService.getUsers.mockResolvedValue({
      users: mockUsers,
      count: mockUsers.length
    });
  });

  it('should render without crashing', async () => {
    render(
      <TestWrapper>
        <UserListTable refreshTrigger={0} />
      </TestWrapper>
    );

    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByText('User List')).toBeInTheDocument();
    });
  });

  it('should handle empty users array', async () => {
    mockAuthService.getUsers.mockResolvedValue({
      users: [],
      count: 0
    });

    render(
      <TestWrapper>
        <UserListTable refreshTrigger={0} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No users found')).toBeInTheDocument();
    });
  });

  it('should handle API errors gracefully', async () => {
    mockAuthService.getUsers.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <UserListTable refreshTrigger={0} />
      </TestWrapper>
    );

    // Component should still render without crashing
    await waitFor(() => {
      expect(screen.getByText('User List')).toBeInTheDocument();
    });
  });

  it('should not make multiple API calls on rapid refreshTrigger changes', async () => {
    let callCount = 0;
    mockAuthService.getUsers.mockImplementation(async () => {
      callCount++;
      return {
        users: mockUsers,
        count: mockUsers.length
      };
    });

    const { rerender } = render(
      <TestWrapper>
        <UserListTable refreshTrigger={0} />
      </TestWrapper>
    );

    // Rapidly change refreshTrigger
    rerender(
      <TestWrapper>
        <UserListTable refreshTrigger={1} />
      </TestWrapper>
    );

    rerender(
      <TestWrapper>
        <UserListTable refreshTrigger={2} />
      </TestWrapper>
    );

    // Wait for any pending calls
    await new Promise(resolve => setTimeout(resolve, 100));

    // Should have made calls for each unique refreshTrigger
    expect(callCount).toBeLessThanOrEqual(3);
  });
});
