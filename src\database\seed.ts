#!/usr/bin/env tsx

/**
 * Database seeding script for Tele-AI
 * Creates initial data for development and testing
 */

import { db } from './connection';
import bcrypt from 'bcryptjs';

interface SeedUser {
  email: string;
  name: string;
  password: string;
  role: 'admin' | 'user';
}

interface SeedLead {
  name: string;
  phone: string;
  email: string;
  notes: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'rejected';
}

const SEED_USERS: SeedUser[] = [
  {
    email: '<EMAIL>',
    name: 'admin',
    password: 'admin123',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    name: 'demo',
    password: 'demo123',
    role: 'user'
  },
  {
    email: '<EMAIL>',
    name: 'testuser',
    password: 'test123',
    role: 'user'
  },
  {
    email: '<EMAIL>',
    name: 'manager',
    password: 'manager123',
    role: 'user'
  }
];

const SEED_LEADS: SeedLead[] = [
  {
    name: '<PERSON>',
    phone: '+1234567890',
    email: '<EMAIL>',
    notes: 'Interested in AI telecalling services for his marketing agency',
    status: 'new'
  },
  {
    name: '<PERSON>',
    phone: '+1987654321',
    email: '<EMAIL>',
    notes: 'Small business owner looking for lead generation solutions',
    status: 'contacted'
  },
  {
    name: 'Bob Johnson',
    phone: '+1555666777',
    email: '<EMAIL>',
    notes: 'Enterprise client interested in bulk calling services',
    status: 'qualified'
  },
  {
    name: 'Alice Brown',
    phone: '+1444555666',
    email: '<EMAIL>',
    notes: 'Real estate agent needing automated follow-up calls',
    status: 'new'
  },
  {
    name: 'Charlie Wilson',
    phone: '+1333444555',
    email: '<EMAIL>',
    notes: 'Insurance company looking for customer outreach automation',
    status: 'contacted'
  },
  {
    name: 'Diana Davis',
    phone: '+1222333444',
    email: '<EMAIL>',
    notes: 'E-commerce business interested in abandoned cart recovery calls',
    status: 'converted'
  }
];

async function seedUsers(): Promise<void> {
  console.log('🌱 Seeding users...');

  for (const userData of SEED_USERS) {
    try {
      // Check if user already exists
      const existingUser = await db.query(
        'SELECT id FROM users WHERE email = $1 OR name = $2',
        [userData.email, userData.name]
      );

      if (existingUser.rows.length > 0) {
        console.log(`   ⏭️  User ${userData.name} already exists, skipping...`);
        continue;
      }

      // Hash password
      const passwordHash = await bcrypt.hash(userData.password, 10);

      // Insert user
      const result = await db.query(
        `INSERT INTO users (email, name, password_hash, role) 
         VALUES ($1, $2, $3, $4) 
         RETURNING id, name, role`,
        [userData.email, userData.name, passwordHash, userData.role]
      );

      console.log(`   ✅ Created user: ${result.rows[0].name} (${result.rows[0].role})`);
    } catch (error) {
      console.error(`   ❌ Failed to create user ${userData.name}:`, error);
    }
  }
}

async function seedLeads(): Promise<void> {
  console.log('🌱 Seeding leads...');

  for (const leadData of SEED_LEADS) {
    try {
      // Check if lead already exists (by phone number)
      const existingLead = await db.query(
        'SELECT id FROM leads WHERE phone = $1',
        [leadData.phone]
      );

      if (existingLead.rows.length > 0) {
        console.log(`   ⏭️  Lead ${leadData.name} already exists, skipping...`);
        continue;
      }

      // Insert lead
      const result = await db.query(
        `INSERT INTO leads (name, phone, email, notes, status) 
         VALUES ($1, $2, $3, $4, $5) 
         RETURNING id, name, status`,
        [leadData.name, leadData.phone, leadData.email, leadData.notes, leadData.status]
      );

      console.log(`   ✅ Created lead: ${result.rows[0].name} (${result.rows[0].status})`);
    } catch (error) {
      console.error(`   ❌ Failed to create lead ${leadData.name}:`, error);
    }
  }
}

async function clearExistingData(): Promise<void> {
  console.log('🧹 Clearing existing seed data...');

  try {
    // Delete seed users (except admin)
    await db.query(
      `DELETE FROM users 
       WHERE email IN ($1, $2, $3) 
       AND role != 'admin'`,
      ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    );

    // Delete seed leads
    const phoneNumbers = SEED_LEADS.map(lead => lead.phone);
    const placeholders = phoneNumbers.map((_, index) => `$${index + 1}`).join(', ');
    await db.query(
      `DELETE FROM leads WHERE phone IN (${placeholders})`,
      phoneNumbers
    );

    console.log('   ✅ Cleared existing seed data');
  } catch (error) {
    console.error('   ❌ Failed to clear existing data:', error);
  }
}

async function verifySeeding(): Promise<void> {
  console.log('🔍 Verifying seeded data...');

  try {
    // Count users
    const userCount = await db.query('SELECT COUNT(*) as count FROM users');
    console.log(`   📊 Total users: ${userCount.rows[0].count}`);

    // Count leads
    const leadCount = await db.query('SELECT COUNT(*) as count FROM leads');
    console.log(`   📊 Total leads: ${leadCount.rows[0].count}`);

    // Count admin users
    const adminCount = await db.query('SELECT COUNT(*) as count FROM users WHERE role = $1', ['admin']);
    console.log(`   👑 Admin users: ${adminCount.rows[0].count}`);

    // List all users
    const users = await db.query('SELECT name, role, email FROM users ORDER BY role DESC, name');
    console.log('   👥 Users:');
    users.rows.forEach((user: any) => {
      console.log(`      - ${user.name} (${user.role}) - ${user.email}`);
    });

  } catch (error) {
    console.error('   ❌ Failed to verify seeded data:', error);
  }
}

async function main(): Promise<void> {
  console.log('🚀 Starting database seeding process...\n');
  await db.connect();
  try {
    // Check database connection
    const isHealthy = await db.healthCheck();
    if (!isHealthy) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful\n');

    // Parse command line arguments
    const args = process.argv.slice(2);
    const shouldClear = args.includes('--clear') || args.includes('-c');
    const usersOnly = args.includes('--users-only');
    const leadsOnly = args.includes('--leads-only');

    if (shouldClear) {
      await clearExistingData();
      console.log();
    }

    if (!leadsOnly) {
      await seedUsers();
      console.log();
    }

    if (!usersOnly) {
      await seedLeads();
      console.log();
    }

    await verifySeeding();

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📝 Default credentials:');
    console.log('   Admin: username=admin, password=admin123');
    console.log('   Demo:  username=demo, password=demo123');
    console.log('   Test:  username=testuser, password=test123');

  } catch (error) {
    console.error('\n❌ Database seeding failed:', error);
    process.exit(1);
  } finally {
    await db.disconnect();
  }
}

// Command line usage help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🌱 Database Seeding Script

Usage: npm run db:seed [options]

Options:
  --clear, -c      Clear existing seed data before seeding
  --users-only     Seed only users (skip leads)
  --leads-only     Seed only leads (skip users)
  --help, -h       Show this help message

Examples:
  npm run db:seed                    # Seed all data
  npm run db:seed --clear            # Clear and reseed all data
  npm run db:seed --users-only       # Seed only users
  npm run db:seed --clear --users-only  # Clear and seed only users
`);
  process.exit(0);
}

// Run the seeding process
if (require.main === module) {
  main();
}

export { main as seedDatabase };
